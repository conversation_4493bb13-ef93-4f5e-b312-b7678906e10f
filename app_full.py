#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整版外卖订餐系统 - 使用SQLite数据库
包含所有原有功能：注册、个人信息管理、菜单管理、订单管理、评论系统等
适合小白用户快速运行和测试
"""

from flask import Flask, render_template, request, redirect, url_for, flash, session
from werkzeug.utils import secure_filename
import sqlite3
import os
import datetime

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # 用于session

# 数据库文件路径
DB_PATH = 'takeaway.db'

# 全局变量
restaurant = ""  # 当前选择的餐厅
notFinishedNum = 0  # 未完成订单数量

# 上传文件要储存的目录
UPLOAD_FOLDER = 'static/images/'
# 允许上传的文件扩展名的集合
ALLOWED_EXTENSIONS = set(['png', 'jpg', 'jpeg'])

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1] in ALLOWED_EXTENSIONS

def init_database():
    """初始化SQLite数据库"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # 创建管理员表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS ADMIN (
            username TEXT PRIMARY KEY,
            password TEXT NOT NULL
        )
    ''')
    
    # 创建顾客表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS CUSTOMER (
            username TEXT PRIMARY KEY,
            password TEXT NOT NULL,
            address TEXT NOT NULL,
            phone TEXT NOT NULL
        )
    ''')
    
    # 创建商家表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS RESTAURANT (
            username TEXT PRIMARY KEY,
            password TEXT NOT NULL,
            address TEXT NOT NULL,
            phone TEXT NOT NULL,
            img_res TEXT
        )
    ''')
    
    # 创建菜品表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS DISHES (
            dishname TEXT PRIMARY KEY,
            restaurant TEXT NOT NULL,
            dishinfo TEXT,
            nutriention TEXT,
            price REAL NOT NULL,
            sales INTEGER NOT NULL DEFAULT 0,
            imgsrc TEXT,
            isSpecialty INTEGER DEFAULT 0,
            FOREIGN KEY (restaurant) REFERENCES RESTAURANT(username)
        )
    ''')
    
    # 创建购物车表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS SHOPPINGCART (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            dishname TEXT NOT NULL,
            restaurant TEXT NOT NULL,
            price REAL NOT NULL,
            quantity INTEGER NOT NULL DEFAULT 1,
            FOREIGN KEY (username) REFERENCES CUSTOMER(username),
            FOREIGN KEY (dishname) REFERENCES DISHES(dishname),
            FOREIGN KEY (restaurant) REFERENCES RESTAURANT(username)
        )
    ''')
    
    # 创建订单评论表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS ORDER_COMMENT (
            orderID INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            restaurant TEXT NOT NULL,
            dishname TEXT NOT NULL,
            price REAL NOT NULL,
            quantity INTEGER NOT NULL DEFAULT 1,
            isFinished INTEGER DEFAULT 0,
            rank INTEGER DEFAULT 0,
            text TEXT DEFAULT '',
            transactiontime DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (username) REFERENCES CUSTOMER(username),
            FOREIGN KEY (restaurant) REFERENCES RESTAURANT(username),
            FOREIGN KEY (dishname) REFERENCES DISHES(dishname)
        )
    ''')
    
    # 插入初始数据
    # 管理员
    cursor.execute("INSERT OR IGNORE INTO ADMIN VALUES ('root', '12345678')")
    
    # 顾客
    cursor.execute("INSERT OR IGNORE INTO CUSTOMER VALUES ('阿楠', '77777777', '西北民族大学学生宿舍10-114', '13844444444')")
    cursor.execute("INSERT OR IGNORE INTO CUSTOMER VALUES ('小巩', '55555555', '西北民族大学学生宿舍10-119', '18833344444')")
    
    # 商家
    cursor.execute("INSERT OR IGNORE INTO RESTAURANT VALUES ('土风土味', '77777777', '甘肃省兰州市榆中县夏官营镇榆民街128号', '1314074', 'static/images/res_2.jpg')")
    cursor.execute("INSERT OR IGNORE INTO RESTAURANT VALUES ('统一面馆', '88888888', '甘肃省兰州市榆中县夏官营镇榆民街69号', '1884801', 'static/images/res_1.jpg')")
    
    # 菜品
    cursor.execute("INSERT OR IGNORE INTO DISHES VALUES ('水煮鱼', '土风土味', '松江鲈鱼，巨口细鳞，肉质鲜嫩', '蛋白质，维生素', 26.00, 0, 'static/images/img_2.jpg', 0)")
    cursor.execute("INSERT OR IGNORE INTO DISHES VALUES ('香锅牛肉', '土风土味', '该香锅牛肉味道鲜美，有土豆藕片等蔬菜可添加', '蛋白质，维生素', 14.50, 0, 'static/images/img_5.jpg', 1)")
    cursor.execute("INSERT OR IGNORE INTO DISHES VALUES ('牛肉面', '统一面馆', '老坛酸菜牛肉面，麻辣酸爽，美味享受', '蛋白质，淀粉，维生素', 13.00, 1, 'static/images/img_7.jpg', 0)")
    cursor.execute("INSERT OR IGNORE INTO DISHES VALUES ('红烧肉', '土风土味', '肥瘦相间，入口即化', '蛋白质，脂肪', 18.00, 5, 'static/images/img_3.jpg', 0)")
    cursor.execute("INSERT OR IGNORE INTO DISHES VALUES ('麻辣烫', '统一面馆', '麻辣鲜香，暖胃暖心', '蛋白质，维生素', 15.00, 3, 'static/images/img_4.jpg', 1)")
    
    # 插入一些测试订单数据
    cursor.execute("INSERT OR IGNORE INTO ORDER_COMMENT (username, restaurant, dishname, price, quantity, isFinished, rank, text) VALUES ('阿楠', '土风土味', '水煮鱼', 26.00, 1, 1, 5, '味道很棒！')")
    cursor.execute("INSERT OR IGNORE INTO ORDER_COMMENT (username, restaurant, dishname, price, quantity, isFinished, rank, text) VALUES ('阿楠', '统一面馆', '牛肉面', 13.00, 2, 0, 0, '')")
    
    conn.commit()
    conn.close()
    print("数据库初始化完成！")

def get_db_connection():
    """获取数据库连接"""
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row  # 使结果可以像字典一样访问
    return conn

@app.route('/')
@app.route('/index')
def index():
    """首页"""
    return render_template('index.html')

# 注册
@app.route('/register', methods=['GET', 'POST'])
def register():
    global username, userRole
    msg = ""
    if request.method == 'GET':
        return render_template('Register.html')
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        phone = request.form.get('phone')
        addr = request.form.get('addr')
        userRole = request.form.get('userRole')
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        if userRole == 'RESTAURANT':
            # 检查商家是否已存在
            cursor.execute("SELECT * FROM RESTAURANT WHERE username = ?", (username,))
            if cursor.fetchone():
                msg = "fail1"
                flash("失败！商家已注册！")
            else:
                try:
                    cursor.execute("INSERT INTO RESTAURANT (username, password, address, phone) VALUES (?, ?, ?, ?)", 
                                 (username, password, addr, phone))
                    conn.commit()
                    msg = "done1"
                    flash("商家注册成功")
                except Exception as e:
                    msg = "fail1"
                    flash("注册出错，失败")
                    
        elif userRole == 'CUSTOMER':
            # 检查顾客是否已存在
            cursor.execute("SELECT * FROM CUSTOMER WHERE username = ?", (username,))
            if cursor.fetchone():
                msg = "fail2"
                flash("失败！顾客已注册！")
            else:
                try:
                    cursor.execute("INSERT INTO CUSTOMER (username, password, address, phone) VALUES (?, ?, ?, ?)", 
                                 (username, password, addr, phone))
                    conn.commit()
                    msg = "done2"
                    flash("顾客注册成功")
                except Exception as e:
                    msg = "fail2"
                    flash("注册出错，失败")
        
        conn.close()
        return render_template('Register.html', messages=msg, username=username, userRole=userRole)

# 登录
@app.route('/logIn', methods=['GET', 'POST'])
def login():
    msg = ""
    if request.method == 'GET':
        return render_template('logIn.html')
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        userRole = request.form.get('userRole')
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        try:
            if userRole == 'ADMIN':
                cursor.execute("SELECT * FROM ADMIN WHERE username = ? AND password = ?", (username, password))
            elif userRole == 'CUSTOMER':
                cursor.execute("SELECT * FROM CUSTOMER WHERE username = ? AND password = ?", (username, password))
            elif userRole == 'RESTAURANT':
                cursor.execute("SELECT * FROM RESTAURANT WHERE username = ? AND password = ?", (username, password))
            
            user = cursor.fetchone()
            
            if user:
                session['username'] = username
                session['userRole'] = userRole
                flash(f'欢迎 {username}！')
                
                if userRole == 'ADMIN':
                    return redirect(url_for('admin_index'))
                elif userRole == 'CUSTOMER':
                    return redirect(url_for('UserRestListPage'))
                elif userRole == 'RESTAURANT':
                    return redirect(url_for('MerchantIndex'))
            else:
                flash('用户名或密码错误')
                return render_template('logIn.html')
                
        except Exception as e:
            print(f"登录错误: {e}")
            flash('登录失败，请重试')
            return render_template('logIn.html')
        finally:
            conn.close()

# 管理员主页
@app.route('/admin')
def admin_index():
    """管理员主页"""
    if 'username' not in session or session.get('userRole') != 'ADMIN':
        return redirect(url_for('login'))
    return render_template('adminRestList.html', username=session['username'])

# 用户登录后显示商家列表
@app.route('/UserRestList', methods=['GET', 'POST'])
def UserRestListPage():
    msg = ""
    if request.method == 'GET':
        if 'username' not in session or session.get('userRole') != 'CUSTOMER':
            return redirect(url_for('login'))

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 查询所有餐厅
        cursor.execute("SELECT * FROM RESTAURANT")
        restaurants = cursor.fetchall()
        conn.close()

        if restaurants:
            msg = "done"
            return render_template('UserRestList.html', username=session['username'], result=restaurants, messages=msg)
        else:
            msg = "none"
            return render_template('UserRestList.html', username=session['username'], messages=msg)

# 选择商家进入菜单列表
@app.route('/Menu', methods=['GET', 'POST'])
def menu():
    msg = ""
    if request.form["action"] == "进入本店":
        restaurant = request.form['restaurant']

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 查询该餐厅的菜品
        cursor.execute("SELECT * FROM DISHES WHERE restaurant = ?", (restaurant,))
        dishes = cursor.fetchall()
        conn.close()

        if dishes:
            msg = "done"
            return render_template('Menu.html', username=session['username'], RESTAURANT=restaurant, result=dishes, messages=msg)
        else:
            msg = "none"
            return render_template('Menu.html', username=session['username'], RESTAURANT=restaurant, messages=msg)

# 查看商家评论
@app.route('/ResComment', methods=['GET', 'POST'])
def resComment():
    msg = ""
    if request.form["action"] == "查看评价":
        restaurant = request.form['restaurant']

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 查询该餐厅的评论
        cursor.execute("SELECT * FROM ORDER_COMMENT WHERE restaurant = ? AND isFinished = 1 AND text != ''", (restaurant,))
        comments = cursor.fetchall()
        conn.close()

        if comments:
            msg = "done"
            return render_template('ResComment.html', username=session['username'], RESTAURANT=restaurant, result=comments, messages=msg)
        else:
            msg = "none"
            return render_template('ResComment.html', username=session['username'], RESTAURANT=restaurant, messages=msg)

# 购物车
@app.route('/myOrder', methods=['GET', 'POST'])
def shoppingCartPage():
    if request.method == 'GET':
        if 'username' not in session:
            return redirect(url_for('login'))

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 查询购物车
        cursor.execute("SELECT * FROM SHOPPINGCART WHERE username = ?", (session['username'],))
        cart_items = cursor.fetchall()
        conn.close()

        if cart_items:
            msg = "done"
            return render_template('myOrder.html', username=session['username'], result=cart_items, messages=msg)
        else:
            msg = "none"
            return render_template('myOrder.html', username=session['username'], messages=msg)

    elif request.form["action"] == "加入购物车":
        dishname = request.form['dishname']
        restaurant = request.form['restaurant']
        price = float(request.form['price'])

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 检查购物车中是否已有该菜品
        cursor.execute("SELECT * FROM SHOPPINGCART WHERE username = ? AND dishname = ? AND restaurant = ?",
                      (session['username'], dishname, restaurant))
        existing = cursor.fetchone()

        if existing:
            # 更新数量
            cursor.execute("UPDATE SHOPPINGCART SET quantity = quantity + 1 WHERE username = ? AND dishname = ? AND restaurant = ?",
                          (session['username'], dishname, restaurant))
        else:
            # 添加新项目
            cursor.execute("INSERT INTO SHOPPINGCART (username, dishname, restaurant, price) VALUES (?, ?, ?, ?)",
                          (session['username'], dishname, restaurant, price))

        conn.commit()
        conn.close()

        flash('已加入购物车！')
        return redirect(url_for('shoppingCartPage'))

# 个人中心页面
@app.route('/personal')
def personalPage():
    if 'username' not in session:
        return redirect(url_for('login'))
    return render_template('personal.html', username=session['username'])

# 商家主页
@app.route('/MerchantIndex')
def MerchantIndex():
    if 'username' not in session or session.get('userRole') != 'RESTAURANT':
        return redirect(url_for('login'))
    return render_template('MerchantIndex.html', username=session['username'])

# 商家个人中心页面
@app.route('/MerchantPersonal')
def MpersonalPage():
    if 'username' not in session or session.get('userRole') != 'RESTAURANT':
        return redirect(url_for('login'))
    return render_template('MerchantPersonal.html', username=session['username'])

@app.route('/logout')
def logout():
    """退出登录"""
    session.clear()
    flash('已成功退出登录')
    return redirect(url_for('index'))

if __name__ == '__main__':
    # 初始化数据库
    init_database()
    
    print("🚀 外卖订餐系统启动中...")
    print("📱 请在浏览器中访问: http://localhost:5000")
    print("👤 测试账号:")
    print("   管理员: root / 12345678")
    print("   顾客: 阿楠 / 77777777")
    print("   商家: 土风土味 / 77777777")
    print("🛑 按 Ctrl+C 停止服务器")
    
    app.run(host='localhost', port=5000, debug=True)
