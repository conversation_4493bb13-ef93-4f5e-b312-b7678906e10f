{
    // 使用 IntelliSense 了解相关属性。 
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch Edge",
            "request": "launch",
            "type": "msedge",
            "url": "http://localhost:8080",
            "webRoot": "${workspaceFolder}"
        },
        {
            "name": "运行外卖订餐系统",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/Takeaways-Order-Sys-master/app_full.py",
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}/Takeaways-Order-Sys-master",
            "python": "${workspaceFolder}/Takeaways-Order-Sys-master/new_venv/Scripts/python.exe",
            "env": {
                "FLASK_ENV": "development",
                "FLASK_DEBUG": "1"
            }
        },
        {"name":"Python 调试程序: 当前文件","type":"debugpy","request":"launch","program":"${file}","console":"integratedTerminal"},
        {
            "name": "Launch Edge",
            "request": "launch",
            "type": "msedge",
            "url": "http://localhost:8080",
            "webRoot": "${workspaceFolder}"
        },
        {
            "type": "msedge",
            "request": "launch",
            "name": "Open index.html",
            "file": "c:\\Users\\<USER>\\Desktop\\Takeaways-Order-Sys-master\\Takeaways-Order-Sys-master\\templates\\index.html"
        }
    ]
}