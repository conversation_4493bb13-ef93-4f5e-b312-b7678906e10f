<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
    <title>买家用户界面</title>
    <!-- <link rel="stylesheet" href="static/css/test.css" type="text/css"> -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/test.css') }}">
    <link href="https://fonts.googleapis.com/css?family=Lato:300,400,700" rel="stylesheet">
	<link href="https://fonts.googleapis.com/css?family=<PERSON><PERSON><PERSON>+<PERSON>ript" rel="stylesheet">

	<!-- Animate.css -->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/animate.css') }}">
	<!-- Icomoon Icon Fonts-->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/icomoon.css') }}">
	<!-- Themify Icons-->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/themify-icons.css') }}">
	<!-- Bootstrap  -->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.css') }}">

	<!-- Magnific Popup -->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/magnific-popup.css') }}">

	<!-- Bootstrap DateTimePicker -->
	<link rel="stylesheet" href="static/css/bootstrap-datetimepicker.min.css">

	<!-- Owl Carousel  -->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/owl.carousel.min.css') }}">
	<link rel="stylesheet" href="{{ url_for('static', filename='css/owl.theme.default.min.css') }}">

	<!-- Theme style  -->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
	<script src="static/js/modernizr-2.6.2.min.js"></script>
</head>
<body>
	<div class="gtco-loader"></div>

	<div id="page">
		<header>
		<nav class="navbar navbar-default navbar-fixed-top" role="navigation">

			<div class="container-fluid">
				<div class="navbar-header">
					<ul class="nav navbar-nav">
						<li><a href="UserRestList">商家列表</a></li>
					</ul>
				</div>
				<div class="collapse navbar-collapse navbar-right" id="navbar-collapse">
					<ul class="nav navbar-nav">
						<li><a href="index">退出登录</a></li>
						<li><a href="personal">个人中心</a></li>&nbsp;
                        <li><a href="myOrder">购物车</a></li>
					</ul>
				</div>
			</div>

		</nav>
		</header>

	<div class="gtco-section">
		<div class="gtco-container">
			<div class="row">
				<div class="col-md-8 col-md-offset-2 text-center gtco-heading">
					<h2 class="cursive-font primary-color">Restaurant List</h2>
				</div>
			</div>
			<div class="row">
					{% if messages == "done" %}
						{% for item in result %}
					<div class="col-lg-6 col-md-6 col-sm-6">
							<div class="fh5co-card-item">
								<a href="{{ url_for('static', filename=item[4]) if item[4] else '#' }}" class="fh5co-card-item image-popup">
								<figure>
									<div class="overlay"><i class="ti-plus"></i></div>
									<img src="{{ url_for('static', filename=item[4]) if item[4] else url_for('static', filename='images/default.jpg') }}" alt="Image" class="img-responsive">
								</figure>
								</a>
								<div class="col-md-8 col-md-offset-2 text-center gtco-heading">
									<h2 class="cursive-font primary-color" style="font-weight: bold;">{{ item[0] }}</h2>
									<h3 class="cursive-font primary-color">ADDRESS：{{ item[2] }}</h3>
									<h3 class="cursive-font primary-color">PHONE：{{ item[3] }}</h3>
								</div>
								<form action ="{{url_for('menu')}}" method = "POST">
									<input name = "restaurant" value="{{item[0]}}" style = "display:none;">
									<input type = "submit" name = "action" value = "进入本店" style="margin: 15px; font-size: 16px; float: right; color: #FBB448; background-color: white; font-size: 16px; outline:none; border: 1px solid #FBB448; border-radius: 10px;">
								</form>
								<form action ="{{url_for('resComment')}}" method = "POST">
									<input name = "restaurant" value="{{item[0]}}" style = "display:none;">
									<input type = "submit" name = "action" value = "查看评价" style="margin: 15px; font-size: 16px; float: right; color: #FBB448; background-color: white; font-size: 16px; outline:none; border: 1px solid #FBB448; border-radius: 10px;">
								</form>
							</div>
					</div>
						{% endfor %}
					{% elif messages == "none" %}
						<!-- <p style="text-align: center;"><strong style="font-size: 18px;">您还没有订单哦！</strong></p> -->
						<div class="alert alert-danger" role="alert">暂无商家</div>
					{% endif %}


			</div>

		</div>
	</div>
	</div>

	<footer id="gtco-footer" role="contentinfo" style="background-image: url(static/images/img_bg_1.jpg)" data-stellar-background-ratio="0.5">
		<div class="overlay"></div>
		<div class="gtco-container">
			<div class="row row-pb-md">

				<div class="col-md-12 text-center">
					<div class="gtco-widget">
						<h3>社区分享</h3>
						<ul class="gtco-social-icons">
							<li><a href="#"><i class="icon-twitter"></i></a></li>
							<li><a href="#"><i class="icon-facebook"></i></a></li>
							<li><a href="#"><i class="icon-linkedin"></i></a></li>
							<li><a href="#"><i class="icon-dribbble"></i></a></li>
						</ul>
					</div>
				</div>

				<div class="col-md-12 text-center copyright">
					<p><big class="block">&copy; 专业综合实训大作业</big>
				</div>

			</div>

		</div>
	</footer>

	<!-- jQuery -->
	<script src="static/js/jquery.min.js"></script>
	<!-- jQuery Easing -->
	<script src="static/js/jquery.easing.1.3.js"></script>
	<!-- Bootstrap -->
	<script src="static/js/bootstrap.min.js"></script>
	<!-- Waypoints -->
	<script src="static/js/jquery.waypoints.min.js"></script>
	<!-- Carousel -->
	<script src="static/js/owl.carousel.min.js"></script>
	<!-- countTo -->
	<script src="static/js/jquery.countTo.js"></script>

	<!-- Stellar Parallax -->
	<script src="static/js/jquery.stellar.min.js"></script>

	<!-- Magnific Popup -->
	<script src="static/js/jquery.magnific-popup.min.js"></script>
	<script src="static/js/magnific-popup-options.js"></script>

	<script src="static/js/moment.min.js"></script>
	<script src="static/js/bootstrap-datetimepicker.min.js"></script>


	<!-- Main -->
	<script src="static/js/main.js"></script>
</body>

</html>