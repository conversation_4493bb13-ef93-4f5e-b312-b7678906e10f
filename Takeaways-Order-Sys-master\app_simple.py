#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整版外卖订餐系统 - 使用SQLite数据库
包含所有原有功能：注册、个人信息管理、菜单管理、订单管理、评论系统等
适合小白用户快速运行和测试
"""

from flask import Flask, render_template, request, redirect, url_for, flash, session
from werkzeug.utils import secure_filename
import sqlite3
import os
import hashlib
import datetime

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # 用于session

# 数据库文件路径
DB_PATH = 'takeaway.db'

# 全局变量
restaurant = ""  # 当前选择的餐厅
notFinishedNum = 0  # 未完成订单数量

# 上传文件要储存的目录
UPLOAD_FOLDER = 'static/images/'
# 允许上传的文件扩展名的集合
ALLOWED_EXTENSIONS = set(['png', 'jpg', 'jpeg'])

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1] in ALLOWED_EXTENSIONS

def init_database():
    """初始化SQLite数据库"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # 创建管理员表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS ADMIN (
            username TEXT PRIMARY KEY,
            password TEXT NOT NULL
        )
    ''')
    
    # 创建顾客表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS CUSTOMER (
            username TEXT PRIMARY KEY,
            password TEXT NOT NULL,
            address TEXT NOT NULL,
            phone TEXT NOT NULL
        )
    ''')
    
    # 创建商家表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS RESTAURANT (
            username TEXT PRIMARY KEY,
            password TEXT NOT NULL,
            address TEXT NOT NULL,
            phone TEXT NOT NULL,
            img_res TEXT
        )
    ''')
    
    # 创建菜品表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS DISHES (
            dishname TEXT PRIMARY KEY,
            restaurant TEXT NOT NULL,
            dishinfo TEXT,
            nutriention TEXT,
            price REAL NOT NULL,
            sales INTEGER NOT NULL DEFAULT 0,
            imgsrc TEXT,
            isSpecialty INTEGER DEFAULT 0,
            FOREIGN KEY (restaurant) REFERENCES RESTAURANT(username)
        )
    ''')
    
    # 插入初始数据
    # 管理员
    cursor.execute("INSERT OR IGNORE INTO ADMIN VALUES ('root', '12345678')")
    
    # 顾客
    cursor.execute("INSERT OR IGNORE INTO CUSTOMER VALUES ('阿楠', '77777777', '西北民族大学学生宿舍10-114', '13844444444')")
    cursor.execute("INSERT OR IGNORE INTO CUSTOMER VALUES ('小巩', '55555555', '西北民族大学学生宿舍10-119', '18833344444')")
    
    # 商家
    cursor.execute("INSERT OR IGNORE INTO RESTAURANT VALUES ('土风土味', '77777777', '甘肃省兰州市榆中县夏官营镇榆民街128号', '1314074', 'static/images/res_2.jpg')")
    cursor.execute("INSERT OR IGNORE INTO RESTAURANT VALUES ('统一面馆', '88888888', '甘肃省兰州市榆中县夏官营镇榆民街69号', '1884801', 'static/images/res_1.jpg')")

    # 菜品
    cursor.execute("INSERT OR IGNORE INTO DISHES VALUES ('水煮鱼', '土风土味', '松江鲈鱼，巨口细鳞，肉质鲜嫩', '蛋白质，维生素', 26.00, 0, 'static/images/img_2.jpg', 0)")
    cursor.execute("INSERT OR IGNORE INTO DISHES VALUES ('香锅牛肉', '土风土味', '该香锅牛肉味道鲜美，有土豆藕片等蔬菜可添加', '蛋白质，维生素', 14.50, 0, 'static/images/img_5.jpg', 1)")
    cursor.execute("INSERT OR IGNORE INTO DISHES VALUES ('牛肉面', '统一面馆', '老坛酸菜牛肉面，麻辣酸爽，美味享受', '蛋白质，淀粉，维生素', 13.00, 1, 'static/images/img_7.jpg', 0)")
    cursor.execute("INSERT OR IGNORE INTO DISHES VALUES ('红烧肉', '土风土味', '肥瘦相间，入口即化', '蛋白质，脂肪', 18.00, 5, 'static/images/img_3.jpg', 0)")
    cursor.execute("INSERT OR IGNORE INTO DISHES VALUES ('麻辣烫', '统一面馆', '麻辣鲜香，暖胃暖心', '蛋白质，维生素', 15.00, 3, 'static/images/img_4.jpg', 1)")

    # 创建购物车表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS SHOPPINGCART (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            dishname TEXT NOT NULL,
            restaurant TEXT NOT NULL,
            price REAL NOT NULL,
            quantity INTEGER NOT NULL DEFAULT 1,
            FOREIGN KEY (username) REFERENCES CUSTOMER(username),
            FOREIGN KEY (dishname) REFERENCES DISHES(dishname),
            FOREIGN KEY (restaurant) REFERENCES RESTAURANT(username)
        )
    ''')

    # 创建订单评论表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS ORDER_COMMENT (
            orderID INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            restaurant TEXT NOT NULL,
            dishname TEXT NOT NULL,
            price REAL NOT NULL,
            quantity INTEGER NOT NULL DEFAULT 1,
            isFinished INTEGER DEFAULT 0,
            rank INTEGER DEFAULT 0,
            text TEXT DEFAULT '',
            transactiontime DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (username) REFERENCES CUSTOMER(username),
            FOREIGN KEY (restaurant) REFERENCES RESTAURANT(username),
            FOREIGN KEY (dishname) REFERENCES DISHES(dishname)
        )
    ''')

    # 插入一些测试订单数据
    cursor.execute("INSERT OR IGNORE INTO ORDER_COMMENT (username, restaurant, dishname, price, quantity, isFinished, rank, text) VALUES ('阿楠', '土风土味', '水煮鱼', 26.00, 1, 1, 5, '味道很棒！')")
    cursor.execute("INSERT OR IGNORE INTO ORDER_COMMENT (username, restaurant, dishname, price, quantity, isFinished, rank, text) VALUES ('阿楠', '统一面馆', '牛肉面', 13.00, 2, 0, 0, '')")

    conn.commit()
    conn.close()
    print("数据库初始化完成！")

def get_db_connection():
    """获取数据库连接"""
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row  # 使结果可以像字典一样访问
    return conn

@app.route('/')
def index():
    """首页"""
    return render_template('index.html')

@app.route('/logIn', methods=['GET', 'POST'])
def login():
    """登录页面"""
    if request.method == 'GET':
        return render_template('logIn.html')
    
    username = request.form.get('username')
    password = request.form.get('password')
    userRole = request.form.get('userRole')
    
    if not username or not password or not userRole:
        flash('请填写完整信息')
        return render_template('logIn.html')
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        if userRole == 'ADMIN':
            cursor.execute("SELECT * FROM ADMIN WHERE username = ? AND password = ?", (username, password))
        elif userRole == 'CUSTOMER':
            cursor.execute("SELECT * FROM CUSTOMER WHERE username = ? AND password = ?", (username, password))
        elif userRole == 'RESTAURANT':
            cursor.execute("SELECT * FROM RESTAURANT WHERE username = ? AND password = ?", (username, password))
        else:
            flash('无效的用户类型')
            return render_template('logIn.html')
        
        user = cursor.fetchone()
        
        if user:
            session['username'] = username
            session['userRole'] = userRole
            flash(f'欢迎 {username}！')
            
            if userRole == 'ADMIN':
                return redirect(url_for('admin_index'))
            elif userRole == 'CUSTOMER':
                return redirect(url_for('customer_index'))
            elif userRole == 'RESTAURANT':
                return redirect(url_for('merchant_index'))
        else:
            flash('用户名或密码错误')
            return render_template('logIn.html')
            
    except Exception as e:
        print(f"登录错误: {e}")
        flash('登录失败，请重试')
        return render_template('logIn.html')
    finally:
        conn.close()

@app.route('/admin')
def admin_index():
    """管理员主页"""
    if 'username' not in session or session.get('userRole') != 'ADMIN':
        return redirect(url_for('login'))

    # 获取系统统计信息
    conn = sqlite3.connect('takeaway.db')
    cursor = conn.cursor()

    try:
        # 统计用户数量
        cursor.execute("SELECT COUNT(*) FROM CUSTOMER")
        customer_count = cursor.fetchone()[0]

        # 统计商家数量
        cursor.execute("SELECT COUNT(*) FROM RESTAURANT")
        restaurant_count = cursor.fetchone()[0]

        # 获取所有用户信息
        cursor.execute("SELECT username, phone, address FROM CUSTOMER")
        customers = cursor.fetchall()

        # 获取所有商家信息
        cursor.execute("SELECT username, phone, address FROM RESTAURANT")
        restaurants = cursor.fetchall()

        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>管理员控制台</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }}
                .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }}
                .header {{ background: #007bff; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
                .stats {{ display: flex; gap: 20px; margin-bottom: 30px; }}
                .stat-card {{ background: #e9ecef; padding: 20px; border-radius: 8px; flex: 1; text-align: center; }}
                .stat-number {{ font-size: 2em; font-weight: bold; color: #007bff; }}
                .table {{ width: 100%; border-collapse: collapse; margin-bottom: 30px; }}
                .table th, .table td {{ border: 1px solid #ddd; padding: 12px; text-align: left; }}
                .table th {{ background: #f8f9fa; font-weight: bold; }}
                .logout-btn {{ background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; }}
                .logout-btn:hover {{ background: #c82333; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🛠️ 管理员控制台</h1>
                    <p>欢迎，{session['username']} 管理员！</p>
                    <a href="/logout" class="logout-btn">退出登录</a>
                </div>

                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number">{customer_count}</div>
                        <div>注册用户</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{restaurant_count}</div>
                        <div>注册商家</div>
                    </div>
                </div>

                <h2>👥 用户列表</h2>
                <table class="table">
                    <thead>
                        <tr>
                            <th>用户名</th>
                            <th>电话</th>
                            <th>地址</th>
                        </tr>
                    </thead>
                    <tbody>
                        {''.join([f'<tr><td>{c[0]}</td><td>{c[1] or "未填写"}</td><td>{c[2] or "未填写"}</td></tr>' for c in customers])}
                    </tbody>
                </table>

                <h2>🏪 商家列表</h2>
                <table class="table">
                    <thead>
                        <tr>
                            <th>商家名</th>
                            <th>电话</th>
                            <th>地址</th>
                        </tr>
                    </thead>
                    <tbody>
                        {''.join([f'<tr><td>{r[0]}</td><td>{r[1] or "未填写"}</td><td>{r[2] or "未填写"}</td></tr>' for r in restaurants])}
                    </tbody>
                </table>
            </div>
        </body>
        </html>
        """

    except Exception as e:
        return f"<h1>管理员页面加载错误: {str(e)}</h1>"
    finally:
        conn.close()

@app.route('/logout')
def logout():
    """退出登录"""
    session.clear()
    flash('已成功退出登录')
    return redirect(url_for('index'))

@app.route('/customer')
def customer_index():
    """顾客主页"""
    if 'username' not in session or session.get('userRole') != 'CUSTOMER':
        return redirect(url_for('login'))

    # 获取餐厅列表
    conn = sqlite3.connect('takeaway.db')
    cursor = conn.cursor()

    try:
        cursor.execute("SELECT username, phone, address FROM RESTAURANT")
        restaurants = cursor.fetchall()

        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>顾客主页</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }}
                .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }}
                .header {{ background: #28a745; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
                .restaurant-card {{ background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #28a745; }}
                .restaurant-name {{ font-size: 1.2em; font-weight: bold; color: #333; }}
                .restaurant-info {{ color: #666; margin-top: 5px; }}
                .logout-btn {{ background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; }}
                .logout-btn:hover {{ background: #c82333; }}
                .no-restaurants {{ text-align: center; color: #666; padding: 40px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🍽️ 顾客主页</h1>
                    <p>欢迎，{session['username']} 顾客！</p>
                    <a href="/logout" class="logout-btn">退出登录</a>
                </div>

                <h2>🏪 可选餐厅</h2>
                {''.join([f'''
                <div class="restaurant-card">
                    <div class="restaurant-name">{r[0]}</div>
                    <div class="restaurant-info">📞 电话: {r[1] or "未提供"}</div>
                    <div class="restaurant-info">📍 地址: {r[2] or "未提供"}</div>
                </div>
                ''' for r in restaurants]) if restaurants else '<div class="no-restaurants">暂无餐厅信息</div>'}
            </div>
        </body>
        </html>
        """

    except Exception as e:
        return f"<h1>顾客页面加载错误: {str(e)}</h1>"
    finally:
        conn.close()

@app.route('/merchant')
def merchant_index():
    """商家主页"""
    if 'username' not in session or session.get('userRole') != 'RESTAURANT':
        return redirect(url_for('login'))

    # 获取商家信息和菜品
    conn = sqlite3.connect('takeaway.db')
    cursor = conn.cursor()

    try:
        # 获取商家信息
        cursor.execute("SELECT username, phone, address FROM RESTAURANT WHERE username = ?", (session['username'],))
        merchant_info = cursor.fetchone()

        # 获取菜品信息
        cursor.execute("SELECT dishname, price, dishinfo FROM DISHES WHERE restaurant = ?", (session['username'],))
        dishes = cursor.fetchall()

        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>商家主页</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }}
                .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }}
                .header {{ background: #fd7e14; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
                .info-card {{ background: #e9ecef; padding: 15px; margin: 10px 0; border-radius: 8px; }}
                .dish-card {{ background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #fd7e14; }}
                .dish-name {{ font-size: 1.1em; font-weight: bold; color: #333; }}
                .dish-price {{ color: #dc3545; font-weight: bold; font-size: 1.1em; }}
                .dish-desc {{ color: #666; margin-top: 5px; }}
                .logout-btn {{ background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; }}
                .logout-btn:hover {{ background: #c82333; }}
                .no-dishes {{ text-align: center; color: #666; padding: 40px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🏪 商家管理中心</h1>
                    <p>欢迎，{session['username']} 商家！</p>
                    <a href="/logout" class="logout-btn">退出登录</a>
                </div>

                <h2>📋 商家信息</h2>
                <div class="info-card">
                    <strong>商家名称:</strong> {merchant_info[0] if merchant_info else "未知"}<br>
                    <strong>联系电话:</strong> {merchant_info[1] if merchant_info and merchant_info[1] else "未填写"}<br>
                    <strong>商家地址:</strong> {merchant_info[2] if merchant_info and merchant_info[2] else "未填写"}
                </div>

                <h2>🍽️ 菜品列表</h2>
                {''.join([f'''
                <div class="dish-card">
                    <div class="dish-name">{d[0]}</div>
                    <div class="dish-price">¥{d[1]}</div>
                    <div class="dish-desc">{d[2] or "暂无描述"}</div>
                </div>
                ''' for d in dishes]) if dishes else '<div class="no-dishes">暂无菜品信息</div>'}
            </div>
        </body>
        </html>
        """

    except Exception as e:
        return f"<h1>商家页面加载错误: {str(e)}</h1>"
    finally:
        conn.close()

@app.route('/restaurants')
def restaurants():
    """餐厅列表"""
    if 'username' not in session:
        return redirect(url_for('login'))
    
    conn = sqlite3.connect('takeaway.db')
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM RESTAURANT")
    restaurants = cursor.fetchall()
    conn.close()
    
    return render_template('UserRestList.html', username=session['username'], restaurants=restaurants)

if __name__ == '__main__':
    # 初始化数据库
    init_database()
    
    print("🚀 外卖订餐系统启动中...")
    print("📱 请在浏览器中访问: http://localhost:5000")
    print("👤 测试账号:")
    print("   管理员: root / 12345678")
    print("   顾客: 阿楠 / 77777777")
    print("   商家: 土风土味 / 77777777")
    print("🛑 按 Ctrl+C 停止服务器")
    
    app.run(host='localhost', port=5000, debug=True)
