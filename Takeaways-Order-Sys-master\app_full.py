#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阿楠の外卖屋 - 完整版外卖订餐系统
Author: 阿楠
Date: 2025-07-10
LastEditors: 阿楠
LastEditTime: 2025-07-10
使用SQLite数据库，包含所有功能：注册、个人信息管理、菜单管理、订单管理、评论系统等
"""

from flask import Flask, render_template, request, redirect, url_for, flash, session
from werkzeug.utils import secure_filename
import sqlite3
import os
import datetime

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # 用于session

# 配置静态文件
app.config['UPLOAD_FOLDER'] = 'static/images/'

# 数据库文件路径
DB_PATH = 'takeaway.db'

# 全局变量
restaurant = ""  # 当前选择的餐厅
notFinishedNum = 0  # 未完成订单数量

# 上传文件要储存的目录
UPLOAD_FOLDER = 'static/images/'
# 允许上传的文件扩展名的集合
ALLOWED_EXTENSIONS = set(['png', 'jpg', 'jpeg'])

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1] in ALLOWED_EXTENSIONS

def init_database():
    """初始化SQLite数据库"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # 创建管理员表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS ADMIN (
            username TEXT PRIMARY KEY,
            password TEXT NOT NULL
        )
    ''')
    
    # 创建顾客表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS CUSTOMER (
            username TEXT PRIMARY KEY,
            password TEXT NOT NULL,
            address TEXT NOT NULL,
            phone TEXT NOT NULL
        )
    ''')
    
    # 创建商家表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS RESTAURANT (
            username TEXT PRIMARY KEY,
            password TEXT NOT NULL,
            address TEXT NOT NULL,
            phone TEXT NOT NULL,
            img_res TEXT
        )
    ''')
    
    # 创建菜品表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS DISHES (
            dishname TEXT PRIMARY KEY,
            restaurant TEXT NOT NULL,
            dishinfo TEXT,
            nutriention TEXT,
            price REAL NOT NULL,
            sales INTEGER NOT NULL DEFAULT 0,
            imgsrc TEXT,
            isSpecialty INTEGER DEFAULT 0,
            FOREIGN KEY (restaurant) REFERENCES RESTAURANT(username)
        )
    ''')
    
    # 创建购物车表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS SHOPPINGCART (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            dishname TEXT NOT NULL,
            restaurant TEXT NOT NULL,
            price REAL NOT NULL,
            quantity INTEGER NOT NULL DEFAULT 1,
            FOREIGN KEY (username) REFERENCES CUSTOMER(username),
            FOREIGN KEY (dishname) REFERENCES DISHES(dishname),
            FOREIGN KEY (restaurant) REFERENCES RESTAURANT(username)
        )
    ''')
    
    # 创建订单评论表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS ORDER_COMMENT (
            orderID INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            restaurant TEXT NOT NULL,
            dishname TEXT NOT NULL,
            price REAL NOT NULL,
            quantity INTEGER NOT NULL DEFAULT 1,
            isFinished INTEGER DEFAULT 0,
            rank INTEGER DEFAULT 0,
            text TEXT DEFAULT '',
            transactiontime DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (username) REFERENCES CUSTOMER(username),
            FOREIGN KEY (restaurant) REFERENCES RESTAURANT(username),
            FOREIGN KEY (dishname) REFERENCES DISHES(dishname)
        )
    ''')
    
    # 插入初始数据
    # 管理员
    cursor.execute("INSERT OR IGNORE INTO ADMIN VALUES ('root', '12345678')")
    
    # 顾客
    cursor.execute("INSERT OR IGNORE INTO CUSTOMER VALUES ('阿楠', '77777777', '西北民族大学学生宿舍10-114', '13844444444')")
    cursor.execute("INSERT OR IGNORE INTO CUSTOMER VALUES ('小巩', '55555555', '西北民族大学学生宿舍10-119', '18833344444')")
    
    # 商家
    cursor.execute("INSERT OR IGNORE INTO RESTAURANT VALUES ('土风土味', '77777777', '甘肃省兰州市榆中县夏官营镇榆民街128号', '1314074', 'images/res_2.jpg')")
    cursor.execute("INSERT OR IGNORE INTO RESTAURANT VALUES ('统一面馆', '88888888', '甘肃省兰州市榆中县夏官营镇榆民街69号', '1884801', 'images/res_1.jpg')")

    # 菜品
    cursor.execute("INSERT OR IGNORE INTO DISHES VALUES ('水煮鱼', '土风土味', '松江鲈鱼，巨口细鳞，肉质鲜嫩', '蛋白质，维生素', 26.00, 0, 'images/img_2.jpg', 0)")
    cursor.execute("INSERT OR IGNORE INTO DISHES VALUES ('香锅牛肉', '土风土味', '该香锅牛肉味道鲜美，有土豆藕片等蔬菜可添加', '蛋白质，维生素', 14.50, 0, 'images/img_5.jpg', 1)")
    cursor.execute("INSERT OR IGNORE INTO DISHES VALUES ('牛肉面', '统一面馆', '老坛酸菜牛肉面，麻辣酸爽，美味享受', '蛋白质，淀粉，维生素', 13.00, 1, 'images/img_7.jpg', 0)")
    cursor.execute("INSERT OR IGNORE INTO DISHES VALUES ('红烧肉', '土风土味', '肥瘦相间，入口即化', '蛋白质，脂肪', 18.00, 5, 'images/img_3.jpg', 0)")
    cursor.execute("INSERT OR IGNORE INTO DISHES VALUES ('麻辣烫', '统一面馆', '麻辣鲜香，暖胃暖心', '蛋白质，维生素', 15.00, 3, 'images/img_4.jpg', 1)")
    
    # 插入一些测试订单数据
    cursor.execute("INSERT OR IGNORE INTO ORDER_COMMENT (username, restaurant, dishname, price, quantity, isFinished, rank, text) VALUES ('阿楠', '土风土味', '水煮鱼', 26.00, 1, 1, 5, '味道很棒！')")
    cursor.execute("INSERT OR IGNORE INTO ORDER_COMMENT (username, restaurant, dishname, price, quantity, isFinished, rank, text) VALUES ('阿楠', '统一面馆', '牛肉面', 13.00, 2, 0, 0, '')")
    
    conn.commit()
    conn.close()
    print("数据库初始化完成！")

def get_db_connection():
    """获取数据库连接"""
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row  # 使结果可以像字典一样访问
    return conn

@app.route('/')
@app.route('/index')
def index():
    """首页"""
    return render_template('index.html')

# 注册
@app.route('/register', methods=['GET', 'POST'])
def register():
    global username, userRole
    msg = ""
    if request.method == 'GET':
        return render_template('Register.html')
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        phone = request.form.get('phone')
        addr = request.form.get('addr')
        userRole = request.form.get('userRole')
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        if userRole == 'RESTAURANT':
            # 检查商家是否已存在
            cursor.execute("SELECT * FROM RESTAURANT WHERE username = ?", (username,))
            if cursor.fetchone():
                msg = "fail1"
                flash("失败！商家已注册！")
            else:
                try:
                    cursor.execute("INSERT INTO RESTAURANT (username, password, address, phone) VALUES (?, ?, ?, ?)", 
                                 (username, password, addr, phone))
                    conn.commit()
                    msg = "done1"
                    flash("商家注册成功")
                except Exception as e:
                    msg = "fail1"
                    flash("注册出错，失败")
                    
        elif userRole == 'CUSTOMER':
            # 检查顾客是否已存在
            cursor.execute("SELECT * FROM CUSTOMER WHERE username = ?", (username,))
            if cursor.fetchone():
                msg = "fail2"
                flash("失败！顾客已注册！")
            else:
                try:
                    cursor.execute("INSERT INTO CUSTOMER (username, password, address, phone) VALUES (?, ?, ?, ?)", 
                                 (username, password, addr, phone))
                    conn.commit()
                    msg = "done2"
                    flash("顾客注册成功")
                except Exception as e:
                    msg = "fail2"
                    flash("注册出错，失败")
        
        conn.close()
        return render_template('Register.html', messages=msg, username=username, userRole=userRole)

# 登录
@app.route('/logIn', methods=['GET', 'POST'])
def login():
    msg = ""
    if request.method == 'GET':
        return render_template('logIn.html')
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        userRole = request.form.get('userRole')
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        try:
            if userRole == 'ADMIN':
                cursor.execute("SELECT * FROM ADMIN WHERE username = ? AND password = ?", (username, password))
            elif userRole == 'CUSTOMER':
                cursor.execute("SELECT * FROM CUSTOMER WHERE username = ? AND password = ?", (username, password))
            elif userRole == 'RESTAURANT':
                cursor.execute("SELECT * FROM RESTAURANT WHERE username = ? AND password = ?", (username, password))
            
            user = cursor.fetchone()
            
            if user:
                session['username'] = username
                session['userRole'] = userRole
                flash(f'欢迎 {username}！')
                
                if userRole == 'ADMIN':
                    return redirect(url_for('admin_index'))
                elif userRole == 'CUSTOMER':
                    return redirect(url_for('UserRestListPage'))
                elif userRole == 'RESTAURANT':
                    return redirect(url_for('MerchantIndex'))
            else:
                flash('用户名或密码错误')
                return render_template('logIn.html')
                
        except Exception as e:
            print(f"登录错误: {e}")
            flash('登录失败，请重试')
            return render_template('logIn.html')
        finally:
            conn.close()

# 管理员主页 - 商家列表管理
@app.route('/admin')
@app.route('/adminRestList', methods=['GET', 'POST'])
def admin_index():
    """管理员主页 - 商家列表管理"""
    if 'username' not in session or session.get('userRole') != 'ADMIN':
        return redirect(url_for('login'))

    if request.method == 'POST':
        # 处理移除商家的操作
        rest_name = request.form.get('RESTName')
        action = request.form.get('action')

        if action == '移除' and rest_name:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            try:
                # 删除相关数据（级联删除）
                cursor.execute("DELETE FROM DISHES WHERE restaurant = ?", (rest_name,))
                cursor.execute("DELETE FROM SHOPPINGCART WHERE restaurant = ?", (rest_name,))
                cursor.execute("DELETE FROM ORDER_COMMENT WHERE restaurant = ?", (rest_name,))
                cursor.execute("DELETE FROM RESTAURANT WHERE username = ?", (rest_name,))
                conn.commit()
                conn.close()
                return render_template('adminRestList.html', username=session['username'], messages="delete")
            except Exception as e:
                conn.close()
                flash('移除失败，请重试！')

    # 获取所有商家信息
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute("SELECT username, password, address, phone, img_res FROM RESTAURANT")
    restaurants = cursor.fetchall()
    conn.close()

    if restaurants:
        return render_template('adminRestList.html', username=session['username'],
                             result=restaurants, messages="done")
    else:
        return render_template('adminRestList.html', username=session['username'], messages="none")

# 用户登录后显示商家列表
@app.route('/UserRestList', methods=['GET', 'POST'])
def UserRestListPage():
    msg = ""
    if request.method == 'GET':
        if 'username' not in session or session.get('userRole') != 'CUSTOMER':
            return redirect(url_for('login'))

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 查询所有餐厅
        cursor.execute("SELECT * FROM RESTAURANT")
        restaurants = cursor.fetchall()
        conn.close()

        if restaurants:
            msg = "done"
            return render_template('UserRestList.html', username=session['username'], result=restaurants, messages=msg)
        else:
            msg = "none"
            return render_template('UserRestList.html', username=session['username'], messages=msg)

# 选择商家进入菜单列表
@app.route('/Menu', methods=['GET', 'POST'])
def menu():
    msg = ""
    if request.form["action"] == "进入本店":
        restaurant = request.form['restaurant']

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 查询该餐厅的菜品
        cursor.execute("SELECT * FROM DISHES WHERE restaurant = ?", (restaurant,))
        dishes = cursor.fetchall()
        conn.close()

        if dishes:
            msg = "done"
            return render_template('Menu.html', username=session['username'], RESTAURANT=restaurant, result=dishes, messages=msg)
        else:
            msg = "none"
            return render_template('Menu.html', username=session['username'], RESTAURANT=restaurant, messages=msg)

# 查看商家评论
@app.route('/ResComment', methods=['GET', 'POST'])
def resComment():
    msg = ""
    if request.form["action"] == "查看评价":
        restaurant = request.form['restaurant']

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 查询该餐厅的评论
        cursor.execute("SELECT * FROM ORDER_COMMENT WHERE restaurant = ? AND isFinished = 1 AND text != ''", (restaurant,))
        comments = cursor.fetchall()
        conn.close()

        if comments:
            msg = "done"
            return render_template('ResComment.html', username=session['username'], RESTAURANT=restaurant, result=comments, messages=msg)
        else:
            msg = "none"
            return render_template('ResComment.html', username=session['username'], RESTAURANT=restaurant, messages=msg)

# 购物车
@app.route('/myOrder', methods=['GET', 'POST'])
def shoppingCartPage():
    if request.method == 'GET':
        if 'username' not in session:
            return redirect(url_for('login'))

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 查询购物车
        cursor.execute("SELECT * FROM SHOPPINGCART WHERE username = ?", (session['username'],))
        cart_items = cursor.fetchall()
        conn.close()

        if cart_items:
            msg = "done"
            return render_template('myOrder.html', username=session['username'], result=cart_items, messages=msg)
        else:
            msg = "none"
            return render_template('myOrder.html', username=session['username'], messages=msg)

    elif request.form["action"] == "加入购物车":
        dishname = request.form['dishname']
        restaurant = request.form['restaurant']
        price = float(request.form['price'])

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 检查购物车中是否已有该菜品
        cursor.execute("SELECT * FROM SHOPPINGCART WHERE username = ? AND dishname = ? AND restaurant = ?",
                      (session['username'], dishname, restaurant))
        existing = cursor.fetchone()

        if existing:
            # 更新数量
            cursor.execute("UPDATE SHOPPINGCART SET quantity = quantity + 1 WHERE username = ? AND dishname = ? AND restaurant = ?",
                          (session['username'], dishname, restaurant))
        else:
            # 添加新项目
            cursor.execute("INSERT INTO SHOPPINGCART (username, dishname, restaurant, price) VALUES (?, ?, ?, ?)",
                          (session['username'], dishname, restaurant, price))

        conn.commit()
        conn.close()

        flash('已加入购物车！')
        return redirect(url_for('shoppingCartPage'))

    elif request.form["action"] == "删除":
        cart_id = request.form['id']

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("DELETE FROM SHOPPINGCART WHERE id = ? AND username = ?", (cart_id, session['username']))
        conn.commit()
        conn.close()

        flash('已从购物车删除！')
        return redirect(url_for('shoppingCartPage'))

    elif request.form["action"] == "结算":
        # 将购物车中的商品转移到订单表
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 获取购物车中的所有商品
        cursor.execute("SELECT * FROM SHOPPINGCART WHERE username = ?", (session['username'],))
        cart_items = cursor.fetchall()

        # 将每个商品添加到订单表
        for item in cart_items:
            cursor.execute("""INSERT INTO ORDER_COMMENT
                            (username, restaurant, dishname, price, quantity, isFinished, rank, text)
                            VALUES (?, ?, ?, ?, ?, 0, 0, '')""",
                          (session['username'], item[2], item[1], item[3], item[4]))

        # 清空购物车
        cursor.execute("DELETE FROM SHOPPINGCART WHERE username = ?", (session['username'],))

        conn.commit()
        conn.close()

        flash('订单提交成功！')
        return redirect(url_for('OrderPage'))

# 个人中心页面
@app.route('/personal')
def personalPage():
    if 'username' not in session:
        return redirect(url_for('login'))
    return render_template('personal.html', username=session['username'])

# 商家主页
@app.route('/MerchantIndex')
def MerchantIndex():
    if 'username' not in session or session.get('userRole') != 'RESTAURANT':
        return redirect(url_for('login'))
    return render_template('MerchantIndex.html', username=session['username'])

# 商家个人中心页面
@app.route('/MerchantPersonal')
def MpersonalPage():
    if 'username' not in session or session.get('userRole') != 'RESTAURANT':
        return redirect(url_for('login'))
    return render_template('MerchantPersonal.html', username=session['username'])

# 商家菜单管理
@app.route('/MerchantMenu', methods=['GET', 'POST'])
def MerchantMenu():
    if 'username' not in session or session.get('userRole') != 'RESTAURANT':
        return redirect(url_for('login'))

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    if request.method == 'POST':
        action = request.form.get('action')

        if action == '删除该菜品':
            dishname = request.form.get('dishname')
            restaurant = request.form.get('restaurant')
            if dishname and restaurant:
                cursor.execute("DELETE FROM DISHES WHERE dishname = ? AND restaurant = ?", (dishname, restaurant))
                conn.commit()

        elif action == '按销量排序':
            cursor.execute("SELECT * FROM DISHES WHERE restaurant = ? ORDER BY sales DESC", (session['username'],))
            dishes = cursor.fetchall()
            conn.close()
            if dishes:
                return render_template('MerchantMenu.html', username=session['username'], result=dishes, messages="done")
            else:
                return render_template('MerchantMenu.html', username=session['username'], messages="none")

        elif action == '按价格排序':
            cursor.execute("SELECT * FROM DISHES WHERE restaurant = ? ORDER BY price ASC", (session['username'],))
            dishes = cursor.fetchall()
            conn.close()
            if dishes:
                return render_template('MerchantMenu.html', username=session['username'], result=dishes, messages="done")
            else:
                return render_template('MerchantMenu.html', username=session['username'], messages="none")

    # 查询该商家的菜品（默认排序）
    cursor.execute("SELECT * FROM DISHES WHERE restaurant = ?", (session['username'],))
    dishes = cursor.fetchall()
    conn.close()

    if dishes:
        msg = "done"
        return render_template('MerchantMenu.html', username=session['username'], result=dishes, messages=msg)
    else:
        msg = "none"
        return render_template('MerchantMenu.html', username=session['username'], messages=msg)

# 商家订单管理
@app.route('/MerchantOrderPage', methods=['GET', 'POST'])
def MerchantOrderPage():
    if 'username' not in session or session.get('userRole') != 'RESTAURANT':
        return redirect(url_for('login'))

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # 处理POST请求（排序功能）
    if request.method == 'POST':
        action = request.form.get('action')

        if action == '按时间排序':
            cursor.execute("""
                SELECT oc.orderID, oc.username, oc.restaurant, oc.dishname, oc.price, oc.quantity,
                       oc.isFinished, oc.rank, oc.text, oc.transactiontime, d.imgsrc
                FROM ORDER_COMMENT oc
                LEFT JOIN DISHES d ON oc.dishname = d.dishname AND oc.restaurant = d.restaurant
                WHERE oc.restaurant = ?
                ORDER BY oc.transactiontime DESC
            """, (session['username'],))
        elif action == '按价格排序':
            cursor.execute("""
                SELECT oc.orderID, oc.username, oc.restaurant, oc.dishname, oc.price, oc.quantity,
                       oc.isFinished, oc.rank, oc.text, oc.transactiontime, d.imgsrc
                FROM ORDER_COMMENT oc
                LEFT JOIN DISHES d ON oc.dishname = d.dishname AND oc.restaurant = d.restaurant
                WHERE oc.restaurant = ?
                ORDER BY oc.price DESC
            """, (session['username'],))
        elif action == '未完成订单':
            cursor.execute("""
                SELECT oc.orderID, oc.username, oc.restaurant, oc.dishname, oc.price, oc.quantity,
                       oc.isFinished, oc.rank, oc.text, oc.transactiontime, d.imgsrc
                FROM ORDER_COMMENT oc
                LEFT JOIN DISHES d ON oc.dishname = d.dishname AND oc.restaurant = d.restaurant
                WHERE oc.restaurant = ? AND oc.isFinished = 0
                ORDER BY oc.transactiontime DESC
            """, (session['username'],))
        else:
            # 默认查询
            cursor.execute("""
                SELECT oc.orderID, oc.username, oc.restaurant, oc.dishname, oc.price, oc.quantity,
                       oc.isFinished, oc.rank, oc.text, oc.transactiontime, d.imgsrc
                FROM ORDER_COMMENT oc
                LEFT JOIN DISHES d ON oc.dishname = d.dishname AND oc.restaurant = d.restaurant
                WHERE oc.restaurant = ?
                ORDER BY oc.transactiontime DESC
            """, (session['username'],))
    else:
        # GET请求 - 默认查询
        cursor.execute("""
            SELECT oc.orderID, oc.username, oc.restaurant, oc.dishname, oc.price, oc.quantity,
                   oc.isFinished, oc.rank, oc.text, oc.transactiontime, d.imgsrc
            FROM ORDER_COMMENT oc
            LEFT JOIN DISHES d ON oc.dishname = d.dishname AND oc.restaurant = d.restaurant
            WHERE oc.restaurant = ?
            ORDER BY oc.transactiontime DESC
        """, (session['username'],))

    orders = cursor.fetchall()

    # 计算未完成订单数量
    cursor.execute("SELECT COUNT(*) FROM ORDER_COMMENT WHERE restaurant = ? AND isFinished = 0", (session['username'],))
    not_finished_count = cursor.fetchone()[0]

    conn.close()

    if orders:
        msg = "done"
        return render_template('MerchantOrderPage.html', username=session['username'], result=orders, messages=msg, notFinishedNum=not_finished_count)
    else:
        msg = "none"
        return render_template('MerchantOrderPage.html', username=session['username'], messages=msg, notFinishedNum=not_finished_count)

# 订单历史页面
@app.route('/OrderPage')
def OrderPage():
    if 'username' not in session or session.get('userRole') != 'CUSTOMER':
        return redirect(url_for('login'))

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # 查询该用户的订单
    cursor.execute("SELECT * FROM ORDER_COMMENT WHERE username = ? ORDER BY transactiontime DESC", (session['username'],))
    orders = cursor.fetchall()
    conn.close()

    if orders:
        msg = "done"
        return render_template('OrderPage.html', username=session['username'], result=orders, messages=msg)
    else:
        msg = "none"
        return render_template('OrderPage.html', username=session['username'], messages=msg)

# 评论表单页面
@app.route('/CommentForm', methods=['GET', 'POST'])
def CommentForm():
    if 'username' not in session or session.get('userRole') != 'CUSTOMER':
        return redirect(url_for('login'))

    if request.method == 'GET':
        orderID = request.args.get('orderID')
        if orderID:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM ORDER_COMMENT WHERE orderID = ? AND username = ?", (orderID, session['username']))
            order = cursor.fetchone()
            conn.close()

            if order:
                return render_template('CommentForm.html', username=session['username'], order=order)

        return redirect(url_for('OrderPage'))

    elif request.method == 'POST':
        orderID = request.form.get('orderID')
        rank = int(request.form.get('rank', 0))
        text = request.form.get('text', '')

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("UPDATE ORDER_COMMENT SET rank = ?, text = ? WHERE orderID = ? AND username = ?",
                      (rank, text, orderID, session['username']))
        conn.commit()
        conn.close()

        flash('评论提交成功！')
        return redirect(url_for('OrderPage'))

# 商家处理订单
@app.route('/ProcessOrder', methods=['POST'])
def ProcessOrder():
    if 'username' not in session or session.get('userRole') != 'RESTAURANT':
        return redirect(url_for('login'))

    orderID = request.form.get('orderID')
    action = request.form.get('action')

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    if action == "完成订单":
        cursor.execute("UPDATE ORDER_COMMENT SET isFinished = 1 WHERE orderID = ? AND restaurant = ?",
                      (orderID, session['username']))
        flash('订单已标记为完成！')
    elif action == "取消订单":
        cursor.execute("DELETE FROM ORDER_COMMENT WHERE orderID = ? AND restaurant = ?",
                      (orderID, session['username']))
        flash('订单已取消！')

    conn.commit()
    conn.close()

    return redirect(url_for('MerchantOrderPage'))

# 添加新菜品
@app.route('/AddDish', methods=['GET', 'POST'])
def AddDish():
    if 'username' not in session or session.get('userRole') != 'RESTAURANT':
        return redirect(url_for('login'))

    if request.method == 'GET':
        return render_template('AddDish.html', username=session['username'])

    elif request.method == 'POST':
        dishname = request.form.get('dishname')
        dishinfo = request.form.get('dishinfo', '')
        nutriention = request.form.get('nutriention', '')
        price = float(request.form.get('price', 0))
        isSpecialty = int(request.form.get('isSpecialty', 0))

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        try:
            cursor.execute("""INSERT INTO DISHES
                            (dishname, restaurant, dishinfo, nutriention, price, sales, imgsrc, isSpecialty)
                            VALUES (?, ?, ?, ?, ?, 0, 'images/default.jpg', ?)""",
                          (dishname, session['username'], dishinfo, nutriention, price, isSpecialty))
            conn.commit()
            flash('菜品添加成功！')
        except sqlite3.IntegrityError:
            flash('菜品名称已存在！')
        except Exception as e:
            flash('添加失败，请重试！')
        finally:
            conn.close()

        return redirect(url_for('MerchantMenu'))

# 删除菜品
@app.route('/DeleteDish', methods=['POST'])
def DeleteDish():
    if 'username' not in session or session.get('userRole') != 'RESTAURANT':
        return redirect(url_for('login'))

    dishname = request.form.get('dishname')

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute("DELETE FROM DISHES WHERE dishname = ? AND restaurant = ?",
                  (dishname, session['username']))
    conn.commit()
    conn.close()

    flash('菜品删除成功！')
    return redirect(url_for('MerchantMenu'))

# 修改个人信息页面
@app.route('/ModifyPersonalInfo', methods=['GET', 'POST'])
def ModifyPersonalInfo():
    if 'username' not in session:
        return redirect(url_for('login'))

    if request.method == 'GET':
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        if session.get('userRole') == 'CUSTOMER':
            cursor.execute("SELECT * FROM CUSTOMER WHERE username = ?", (session['username'],))
        elif session.get('userRole') == 'RESTAURANT':
            cursor.execute("SELECT * FROM RESTAURANT WHERE username = ?", (session['username'],))

        user_info = cursor.fetchone()
        conn.close()

        return render_template('ModifyPersonalInfo.html', username=session['username'], user_info=user_info)

    elif request.method == 'POST':
        address = request.form.get('address')
        phone = request.form.get('phone')

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        try:
            if session.get('userRole') == 'CUSTOMER':
                cursor.execute("UPDATE CUSTOMER SET address = ?, phone = ? WHERE username = ?",
                              (address, phone, session['username']))
            elif session.get('userRole') == 'RESTAURANT':
                cursor.execute("UPDATE RESTAURANT SET address = ?, phone = ? WHERE username = ?",
                              (address, phone, session['username']))

            conn.commit()
            flash('个人信息修改成功！')
        except Exception as e:
            flash('修改失败，请重试！')
        finally:
            conn.close()

        return redirect(url_for('personalPage'))

# 修改密码页面
@app.route('/ModifyPassword', methods=['GET', 'POST'])
def ModifyPassword():
    if 'username' not in session:
        return redirect(url_for('login'))

    if request.method == 'GET':
        return render_template('ModifyPassword.html', username=session['username'])

    elif request.method == 'POST':
        old_password = request.form.get('old_password')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')

        if new_password != confirm_password:
            flash('新密码和确认密码不一致！')
            return render_template('ModifyPassword.html', username=session['username'])

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        try:
            # 验证旧密码
            if session.get('userRole') == 'CUSTOMER':
                cursor.execute("SELECT password FROM CUSTOMER WHERE username = ?", (session['username'],))
            elif session.get('userRole') == 'RESTAURANT':
                cursor.execute("SELECT password FROM RESTAURANT WHERE username = ?", (session['username'],))
            elif session.get('userRole') == 'ADMIN':
                cursor.execute("SELECT password FROM ADMIN WHERE username = ?", (session['username'],))

            current_password = cursor.fetchone()[0]

            if old_password != current_password:
                flash('原密码错误！')
                return render_template('ModifyPassword.html', username=session['username'])

            # 更新密码
            if session.get('userRole') == 'CUSTOMER':
                cursor.execute("UPDATE CUSTOMER SET password = ? WHERE username = ?",
                              (new_password, session['username']))
            elif session.get('userRole') == 'RESTAURANT':
                cursor.execute("UPDATE RESTAURANT SET password = ? WHERE username = ?",
                              (new_password, session['username']))
            elif session.get('userRole') == 'ADMIN':
                cursor.execute("UPDATE ADMIN SET password = ? WHERE username = ?",
                              (new_password, session['username']))

            conn.commit()
            flash('密码修改成功！')
        except Exception as e:
            flash('修改失败，请重试！')
        finally:
            conn.close()

        return redirect(url_for('personalPage'))

# 商家修改个人信息
@app.route('/MerchantModifyPerInfo', methods=['GET', 'POST'])
def MerchantModifyPerInfo():
    if 'username' not in session or session.get('userRole') != 'RESTAURANT':
        return redirect(url_for('login'))

    if request.method == 'GET':
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM RESTAURANT WHERE username = ?", (session['username'],))
        user_info = cursor.fetchone()
        conn.close()

        return render_template('MerchantModifyPerInfo.html', username=session['username'], user_info=user_info)

    elif request.method == 'POST':
        address = request.form.get('address')
        phone = request.form.get('phone')

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        try:
            cursor.execute("UPDATE RESTAURANT SET address = ?, phone = ? WHERE username = ?",
                          (address, phone, session['username']))
            conn.commit()
            flash('商家信息修改成功！')
        except Exception as e:
            flash('修改失败，请重试！')
        finally:
            conn.close()

        return redirect(url_for('MpersonalPage'))

# 商家修改密码
@app.route('/MerchantModifyPwd', methods=['GET', 'POST'])
def MerchantModifyPwd():
    if 'username' not in session or session.get('userRole') != 'RESTAURANT':
        return redirect(url_for('login'))

    if request.method == 'GET':
        return render_template('MerchantModifyPwd.html', username=session['username'])

    elif request.method == 'POST':
        old_password = request.form.get('old_password')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')

        if new_password != confirm_password:
            flash('新密码和确认密码不一致！')
            return render_template('MerchantModifyPwd.html', username=session['username'])

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        try:
            cursor.execute("SELECT password FROM RESTAURANT WHERE username = ?", (session['username'],))
            current_password = cursor.fetchone()[0]

            if old_password != current_password:
                flash('原密码错误！')
                return render_template('MerchantModifyPwd.html', username=session['username'])

            cursor.execute("UPDATE RESTAURANT SET password = ? WHERE username = ?",
                          (new_password, session['username']))
            conn.commit()
            flash('密码修改成功！')
        except Exception as e:
            flash('修改失败，请重试！')
        finally:
            conn.close()

        return redirect(url_for('MpersonalPage'))

# 我的评论页面
@app.route('/MyComments')
def MyComments():
    if 'username' not in session or session.get('userRole') != 'CUSTOMER':
        return redirect(url_for('login'))

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # 查询该用户的所有评论
    cursor.execute("SELECT * FROM ORDER_COMMENT WHERE username = ? AND text != '' ORDER BY transactiontime DESC",
                  (session['username'],))
    comments = cursor.fetchall()
    conn.close()

    if comments:
        msg = "done"
        return render_template('MyComments.html', username=session['username'], result=comments, messages=msg)
    else:
        msg = "none"
        return render_template('MyComments.html', username=session['username'], messages=msg)

# 写评论页面
@app.route('/WriteComments', methods=['GET', 'POST'])
def WriteComments():
    if 'username' not in session or session.get('userRole') != 'CUSTOMER':
        return redirect(url_for('login'))

    if request.method == 'GET':
        orderID = request.args.get('orderID')
        if orderID:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM ORDER_COMMENT WHERE orderID = ? AND username = ?",
                          (orderID, session['username']))
            order = cursor.fetchone()
            conn.close()

            if order:
                return render_template('WriteComments.html', username=session['username'], order=order)

        return redirect(url_for('OrderPage'))

    elif request.method == 'POST':
        orderID = request.form.get('orderID')
        rank = int(request.form.get('rank', 0))
        text = request.form.get('text', '')

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("UPDATE ORDER_COMMENT SET rank = ?, text = ? WHERE orderID = ? AND username = ?",
                      (rank, text, orderID, session['username']))
        conn.commit()
        conn.close()

        flash('评论提交成功！')
        return redirect(url_for('MyComments'))

# 菜单添加页面
@app.route('/MenuAdd', methods=['GET', 'POST'])
def MenuAdd():
    if 'username' not in session or session.get('userRole') != 'RESTAURANT':
        return redirect(url_for('login'))

    if request.method == 'GET':
        return render_template('MenuAdd.html', username=session['username'])

    elif request.method == 'POST':
        action = request.form.get('action')

        # 如果是从商家菜单页面点击"增加菜品"按钮
        if action == '增加菜品':
            return render_template('MenuAdd.html', username=session['username'])

        # 如果是提交添加菜品表单
        dishname = request.form.get('dishname')
        dishinfo = request.form.get('dishinfo', '')
        nutriention = request.form.get('nutriention', '')
        price_str = request.form.get('price', '0')
        isSpecialty = int(request.form.get('isSpecialty', 0))

        try:
            price = float(price_str)
        except ValueError:
            flash('价格格式不正确！')
            return render_template('MenuAdd.html', username=session['username'])

        if not dishname:
            flash('菜品名称不能为空！')
            return render_template('MenuAdd.html', username=session['username'])

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        try:
            cursor.execute("""INSERT INTO DISHES
                            (dishname, restaurant, dishinfo, nutriention, price, sales, imgsrc, isSpecialty)
                            VALUES (?, ?, ?, ?, ?, 0, 'images/default.jpg', ?)""",
                          (dishname, session['username'], dishinfo, nutriention, price, isSpecialty))
            conn.commit()
            flash('菜品添加成功！')
        except sqlite3.IntegrityError:
            flash('菜品名称已存在！')
        except Exception as e:
            flash('添加失败，请重试！')
        finally:
            conn.close()

        return redirect(url_for('MerchantMenu'))

# 菜单修改页面
@app.route('/MenuModify', methods=['GET', 'POST'])
def MenuModify():
    if 'username' not in session or session.get('userRole') != 'RESTAURANT':
        return redirect(url_for('login'))

    if request.method == 'GET':
        dishname = request.args.get('dishname')
        if dishname:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM DISHES WHERE dishname = ? AND restaurant = ?",
                          (dishname, session['username']))
            dish = cursor.fetchone()
            conn.close()

            if dish:
                return render_template('MenuModify.html', username=session['username'], dish=dish)

        return redirect(url_for('MerchantMenu'))

    elif request.method == 'POST':
        action = request.form.get('action')

        # 如果是从商家菜单页面点击"修改菜品信息"按钮
        if action == '修改菜品信息':
            dishname = request.form.get('dishname')
            dishinfo = request.form.get('dishinfo', '')
            nutriention = request.form.get('nutriention', '')
            price = request.form.get('price', '0')
            isSpecialty = request.form.get('isSpecialty', '0')

            # 构造菜品数据传递给模板
            dish = (dishname, session['username'], dishinfo, nutriention, float(price), 0, '', int(isSpecialty))
            return render_template('MenuModify.html', username=session['username'], dish=dish)

        # 如果是提交修改菜品表单
        dishname = request.form.get('dishname')
        dishinfo = request.form.get('dishinfo', '')
        nutriention = request.form.get('nutriention', '')
        price_str = request.form.get('price', '0')
        isSpecialty = int(request.form.get('isSpecialty', 0))

        try:
            price = float(price_str)
        except ValueError:
            flash('价格格式不正确！')
            return redirect(url_for('MerchantMenu'))

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        try:
            cursor.execute("""UPDATE DISHES SET dishinfo = ?, nutriention = ?, price = ?, isSpecialty = ?
                            WHERE dishname = ? AND restaurant = ?""",
                          (dishinfo, nutriention, price, isSpecialty, dishname, session['username']))
            conn.commit()
            flash('菜品修改成功！')
        except Exception as e:
            flash('修改失败，请重试！')
        finally:
            conn.close()

        return redirect(url_for('MerchantMenu'))

# 餐厅评论列表页面
@app.route('/ResCommentList')
def ResCommentList():
    if 'username' not in session or session.get('userRole') != 'RESTAURANT':
        return redirect(url_for('login'))

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # 查询该餐厅的所有评论
    cursor.execute("SELECT * FROM ORDER_COMMENT WHERE restaurant = ? AND text != '' ORDER BY transactiontime DESC",
                  (session['username'],))
    comments = cursor.fetchall()
    conn.close()

    if comments:
        msg = "done"
        return render_template('ResCommentList.html', username=session['username'], result=comments, messages=msg)
    else:
        msg = "none"
        return render_template('ResCommentList.html', username=session['username'], messages=msg)

# 管理员评论列表页面
@app.route('/adminCommentList')
def adminCommentList():
    if 'username' not in session or session.get('userRole') != 'ADMIN':
        return redirect(url_for('login'))

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # 查询所有评论
    cursor.execute("SELECT * FROM ORDER_COMMENT WHERE text != '' ORDER BY transactiontime DESC")
    comments = cursor.fetchall()
    conn.close()

    if comments:
        msg = "done"
        return render_template('adminCommentList.html', username=session['username'], result=comments, messages=msg)
    else:
        msg = "none"
        return render_template('adminCommentList.html', username=session['username'], messages=msg)

@app.route('/logout')
def logout():
    """退出登录"""
    session.clear()
    flash('已成功退出登录')
    return redirect(url_for('index'))

if __name__ == '__main__':
    # 初始化数据库
    init_database()
    
    print("🚀 阿楠の外卖屋启动中...")
    print("📱 请在浏览器中访问: http://localhost:5000")
    print("👤 测试账号:")
    print("   管理员: root / 12345678")
    print("   顾客: 阿楠 / 77777777")
    print("   商家: 土风土味 / 77777777")
    print("🛑 按 Ctrl+C 停止服务器")
    
    app.run(host='localhost', port=5000, debug=True)
