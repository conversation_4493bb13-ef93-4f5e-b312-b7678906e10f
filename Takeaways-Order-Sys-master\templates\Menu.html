<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>菜单界面</title>
	<link rel="stylesheet" href="static/css/OrderPage.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="static/js/modernizr-2.6.2.min.js"></script>

    <link href="https://fonts.googleapis.com/css?family=Lato:300,400,700" rel="stylesheet">
	<link href="https://fonts.googleapis.com/css?family=<PERSON><PERSON><PERSON>+<PERSON>ript" rel="stylesheet">
	<!-- Animate.css -->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/animate.css') }}">
	<!-- Icomoon Icon Fonts-->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/icomoon.css') }}">
	<!-- Themify Icons-->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/themify-icons.css') }}">
	<!-- Bootstrap  -->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.css') }}">
	<!-- Magnific Popup -->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/magnific-popup.css') }}">
	<!-- Bootstrap DateTimePicker -->
	<link rel="stylesheet" href="static/css/bootstrap-datetimepicker.min.css">
	<!-- Owl Carousel  -->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/owl.carousel.min.css') }}">
	<link rel="stylesheet" href="{{ url_for('static', filename='css/owl.theme.default.min.css') }}">


</head>
<body>
	<div class="navbkg" >
    <nav class="gtco-nav" role="navigation" style="background-color: rgba(0, 0, 0, 0.8);">
        <div class="gtco-container">
            <div class="row">
                <div class="col-sm-4 col-xs-12">
                    <div id="gtco-logo"><a href="Menu.html">商家菜单<em></em></a></div>
                </div>
                <div class="col-xs-8 text-right menu-1">
                    <ul>
                        <li><a href="UserRestList">商家列表</a></li>
						<li><a href="ResComment">商家评价</a></li>
						<li><a href="myOrder">购物车</a></li>
						<li class="has-dropdown">
                            <a href="personal">个人中心</a>
                            <ul class="dropdown">
								<li><a href="ModifyPersonalInfo">修改个人信息</a></li>
								<li><a href="ModifyPassword">修改密码</a></li>
                                <li><a href="OrderPage">我的订单</a></li>
                                <li><a href="MyComments">我的评价</a></li>
                                <li><a href="WriteComments">发表评价</a></li>
                            </ul>
						</li>
                    </ul>
                </div>
            </div>
        </div>
	</nav>
	</div>

	<div class="gtco-section">
		<div class="gtco-container">
			<!-- 文字 -->
			<div class="row">
				<div class="col-md-8 col-md-offset-2 text-center gtco-heading">
					<p style="font-size: 40px; color: #FBB448; font-family: Helvetica; font-weight: bold;">今日菜单</p>
					<!-- <p>Dignissimos asperiores vitae velit veniam totam fuga molestias accusamus alias autem provident. Odit ab aliquam dolor eius.</p> -->
				</div>
			</div>
			<!-- TODO: 此处还想加一个按照时间顺序或价格排列 -->
			<div class="selectcondition">
				<form action="{{ url_for('menu') }}" method="post">
					<input type="submit" name="action" value="特色菜" style="color: white; font-size: 16px; outline:none; border:0px; background-color: #FBB448; border-radius: 10px;">
					<input type="submit" name="action" value="按销量排序" style="color: white; font-size: 16px; outline:none; border:0px; background-color: #FBB448; border-radius: 10px;">
					<input type="submit" name="action" value="按价格排序" style="color: white; font-size: 16px; outline:none; border:0px; background-color: #FBB448; border-radius: 10px;">
				</form>
			</div>
			<br/><br/>
			<!-- 展示我的全部订单 -->
			<div class="row">
				<div class="col-lg-12 col-md-4 col-sm-6">
					{% if messages == "done" %}
						{% for item in result %}
							<div class="fh5co-card-item">
								<figure class="col-lg-4" style="float: left;">
									<div class="overlay"><i class="ti-plus"></i></div>
									<img src="{{ url_for('static', filename=item[6]) if item[6] else url_for('static', filename='images/default.jpg') }}" alt="Image" class="img-responsive">
								</figure>
								<div class="fh5co-text">
                                    {% if item[7] == 1 %}
                                    <h1 style="margin-top: 0px;">特色菜</h1>
                                    {% endif %}
									<h2 style="margin-top: 40px; font-weight: bold;"> 菜品：{{ item[0] }} </h2>
									<p>菜品描述：{{ item[2] }}</p>
									<p>营养成分：{{ item[3] }}</p>
                                    <p>销量：{{item[5] }}</p>
									<p style="margin-left: 322px;">价格：<span class="price cursive-font">￥{{ item[4]}}</span></p>
									<form action="{{ url_for('shoppingCartPage') }}" method="POST">
										<input name="restaurant" value="{{ item[1] }}" style="display: none;">
                                        <input name="dishname" value="{{ item[0] }}" style="display: none;">
                                        <input name="price" value="{{ item[4] }}" style="display: none;">
                                        <input name="img_res" value="{{ item[6] }}" style="display: none;">
                                        <input type="submit" name="action" value="加入购物车" style="margin: 15px; font-size: 16px; float: right; color: #FBB448; background-color: white; font-size: 16px; outline:none; border: 1px solid #FBB448; border-radius: 10px;">

                                    </form>
                                    <br/>
								</div>
							</div>
						{% endfor %}
					<!-- TODO -->

					{% elif messages == "none" %}
						<!-- <p style="text-align: center;"><strong style="font-size: 18px;">您还没有订单哦！</strong></p> -->
						<div class="alert alert-danger" role="alert">店铺信息尚不完善！</div>
					{% elif messages == "fail" %}
						<script>alert("该菜品已经存在于购物车中！")</script>
					{% endif %}

				</div>


			</div>
		</div>
	</div>
	<div>
	</div>


    	<!-- jQuery -->
	<script src="static/js/jquery.min.js"></script>
	<!-- jQuery Easing -->
	<script src="static/js/jquery.easing.1.3.js"></script>
	<!-- Bootstrap -->
	<script src="static/js/bootstrap.min.js"></script>
	<!-- Waypoints -->
	<script src="static/js/jquery.waypoints.min.js"></script>
	<!-- Carousel -->
	<script src="static/js/owl.carousel.min.js"></script>
	<!-- countTo -->
	<script src="static/js/jquery.countTo.js"></script>

	<!-- Stellar Parallax -->
	<script src="static/js/jquery.stellar.min.js"></script>

	<!-- Magnific Popup -->
	<script src="static/js/jquery.magnific-popup.min.js"></script>
	<script src="static/js/magnific-popup-options.js"></script>

	<script src="static/js/moment.min.js"></script>
	<script src="static/js/bootstrap-datetimepicker.min.js"></script>


	<!-- Main -->
	<script src="static/js/main.js"></script>
</body>
</html>