# 专业实习项目设计报告

## 项目基本信息
- **项目名称**: 阿楠の外卖屋 - 在线外卖订餐系统
- **开发者**: 阿楠
- **项目版本**: 2.0
- **开发时间**: 2025年7月
- **项目类型**: 专业综合实训大作业

---

## 1. 实训简介

### 项目整体功能简介
本项目是一个基于Web的在线外卖订餐系统《阿楠の外卖屋》，采用Flask框架和SQLite数据库开发，实现了完整的外卖订餐业务流程。系统支持三种用户角色（管理员、商家、顾客），提供了从用户注册、商家入驻、菜品管理、订单处理到评价反馈的全流程服务。

### 知识简介
项目涵盖了Web开发的核心技术栈，包括：
- **后端开发**: Python Flask框架、SQLite数据库操作、RESTful API设计
- **前端技术**: HTML5、CSS3、JavaScript、Bootstrap响应式布局
- **数据库设计**: 关系型数据库设计、SQL语句编写、数据表关联
- **系统架构**: MVC架构模式、会话管理、用户权限控制
- **软件工程**: 项目结构设计、代码规范、版本控制

---

## 2. 开发环境

### 集成开发环境
- **主要IDE**: Visual Studio Code
- **Python环境**: Python 3.14
- **虚拟环境**: venv (Python虚拟环境管理)

### 开发工具
- **代码编辑器**: Visual Studio Code
- **浏览器调试**: Chrome DevTools
- **数据库管理**: SQLite Browser
- **版本控制**: Git

### 项目依赖
- **Web框架**: Flask 2.3.3
- **WSGI工具**: Werkzeug 2.3.7
- **数据库**: SQLite3 (Python内置)
- **模板引擎**: Jinja2 (Flask内置)
- **前端框架**: Bootstrap 3.3.5
- **字体图标**: Font Awesome

### 运行环境
- **操作系统**: Windows 11
- **Python版本**: 3.14
- **Web服务器**: Flask开发服务器
- **数据库**: SQLite (文件数据库)

---

## 3. 代码行数

### 核心代码统计
- **主程序文件**: app_full.py (1,151行)
- **简化版本**: app_simple.py (474行)
- **原始版本**: app.py (约800行)
- **HTML模板**: 26个模板文件 (约2,000行)
- **CSS样式**: 多个样式文件 (约1,500行)
- **JavaScript**: 交互脚本 (约500行)

### 总代码量
- **Python后端代码**: 约2,425行
- **前端代码(HTML/CSS/JS)**: 约4,000行
- **配置和文档**: 约300行
- **项目总代码量**: 约6,725行

---

## 4. 学习周期
**总学习周期**: 20天

### 学习阶段分解
- **第1-3天**: Python基础和Flask框架学习
- **第4-6天**: 数据库设计和SQLite操作
- **第7-10天**: 前端技术(HTML/CSS/JavaScript)学习
- **第11-15天**: 系统功能开发和调试
- **第16-18天**: 系统测试和优化
- **第19-20天**: 项目部署和文档编写

---

## 5. 完成情况

### 项目概述
我们的项目是一个创新的在线外卖订餐平台，它集成了多种用户友好的功能，为外卖行业提供了一个全面的数字化解决方案。该平台的核心特色包括：

### 核心功能模块

#### 🔐 用户管理系统
- **多角色支持**: 管理员、商家、顾客三种用户类型
- **安全认证**: 用户注册、登录、密码加密存储
- **权限控制**: 基于角色的访问控制(RBAC)
- **个人信息管理**: 用户资料修改、密码更新

#### 🏪 商家管理模块
- **商家入驻**: 商家注册和资质管理
- **店铺管理**: 店铺信息维护、图片上传
- **菜品管理**: 菜品增删改查、价格设置、特色标记
- **订单处理**: 订单接收、状态更新、完成确认

#### 🛒 顾客服务模块
- **商家浏览**: 商家列表查看、筛选搜索
- **菜品选择**: 菜单浏览、价格对比、特色推荐
- **购物车功能**: 商品添加、数量修改、批量管理
- **订单管理**: 下单结算、订单跟踪、历史查询

#### 💬 评价系统
- **订单评价**: 5星评分系统、文字评论
- **评价管理**: 评价查看、回复功能
- **信誉体系**: 商家评分统计、信誉排名

#### 🛡️ 管理员功能
- **系统监控**: 用户数据统计、订单监控
- **商家管理**: 商家审核、违规处理
- **数据分析**: 平台运营数据分析

### 技术实现亮点

#### 🏗️ 系统架构
- **MVC模式**: 清晰的模型-视图-控制器分离
- **模块化设计**: 功能模块独立，便于维护扩展
- **RESTful API**: 标准化的接口设计

#### 💾 数据库设计
- **6个核心数据表**: ADMIN、CUSTOMER、RESTAURANT、DISHES、SHOPPINGCART、ORDER_COMMENT
- **外键约束**: 保证数据一致性和完整性
- **索引优化**: 提高查询性能

#### 🎨 用户界面
- **响应式设计**: 适配不同屏幕尺寸
- **用户体验**: 直观的操作流程、友好的错误提示
- **视觉设计**: 现代化的界面风格、统一的视觉规范

### 项目知识点总结

#### Python Web开发
- **Flask框架**: 路由设计、模板渲染、会话管理
- **数据库操作**: SQLite连接、SQL语句执行、事务处理
- **文件处理**: 图片上传、文件存储、安全验证

#### 前端技术
- **HTML5**: 语义化标签、表单设计、页面结构
- **CSS3**: 样式设计、布局技术、响应式设计
- **JavaScript**: DOM操作、事件处理、AJAX交互

#### 数据库技术
- **关系型数据库**: 表结构设计、关联关系、数据规范化
- **SQL语言**: 增删改查、联表查询、聚合函数
- **数据完整性**: 主键、外键、约束条件

#### 软件工程
- **项目管理**: 需求分析、系统设计、开发计划
- **代码规范**: 命名规范、注释规范、结构规范
- **测试调试**: 功能测试、错误处理、性能优化

### 项目成果
✅ **功能完整性**: 实现了外卖订餐系统的所有核心功能  
✅ **技术先进性**: 采用现代Web开发技术栈  
✅ **用户体验**: 界面友好，操作简便  
✅ **代码质量**: 结构清晰，注释完整  
✅ **可扩展性**: 模块化设计，便于功能扩展  

---

## 总结

通过本次专业实习项目，我深入学习了Web开发的完整技术栈，从后端的Python Flask框架到前端的HTML/CSS/JavaScript，从数据库设计到系统架构，全面提升了软件开发能力。项目不仅实现了预期的功能目标，更重要的是培养了系统性思维和工程化开发的能力，为今后的职业发展奠定了坚实的基础。
