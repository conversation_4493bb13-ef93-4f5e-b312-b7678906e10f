@charset "utf-8";
*{margin:0;
padding:0;
list-style-type:none;}

a
{color:#666;
text-decoration:none;}

table
{border-collapse:collapse;
border-spacing:0;
border:0;}

body
{color:#666;
font:12px/180% Arial, Helvetica, sans-serif, "新宋体";}

clearfix:after
{content:".";
display:block;
height:0;
clear:both;
visibility:hidden}

.clearfix
{display:inline-table}

*html .clearfix
{height:1%}

.clearfix
{display:block}

*+html .clearfix
{min-height:1%}

.fl
{float:left;}

.fr
{float:right;}
/*素材家园 - www.sucaijiayuan.com*/
.catbox
{width:940px;
margin:100px auto;}

.catbox table
{text-align:center;
width:100%;}

.catbox table th,.catbox table td
{border:1px solid #CADEFF;}

.catbox table th
{background:#e2f2ff;
border-top:3px solid #a7cbff;
height:30px;}

.catbox table td
{padding:10px;
color:#444;}

.catbox table tbody tr:hover
{background:RGB(238,246,255);}

.checkbox
{width:60px;}

.check-all
{ vertical-align:middle;}

.goods
{width:300px;}

.goods span
{width:180px;
margin-top:20px;
text-align:left;
float:left;}

.goods img
{width:100px;
height:80px;
margin-right:10px;
float:left;}

.price
{width:130px;}

.count
{width:30px;}

.count .add, .count input, .count .reduce
{float:left;
margin-right:-1px;
position:relative;
z-index:0;}

.count .add, .count .reduce
{height:23px;
width:17px;
border:1px solid #e5e5e5;
background:#f0f0f0;
text-align:center;
line-height:23px;
color:#444;}

.count .add:hover, .count .reduce:hover{color:#f50;z-index:3;border-color:#f60;cursor:pointer;}
.count input{width:50px;height:15px;line-height:15px;border:1px solid #aaa;color:#343434;text-align:center;padding:4px 0;background-color:#fff;z-index:2;}
.subtotal{width:150px;color:red;font-weight:bold;}
.operation span:hover,a:hover{cursor:pointer;color:red;text-decoration:underline;}

.foot{margin-top:0px;color:#666;height:48px;border:1px solid #c8c8c8;border-top:0;background-color:#eaeaea;background-image:linear-gradient(RGB(241,241,241),RGB(226,226,226));position:relative;z-index:8;}
.foot div, .foot a{line-height:48px;height:48px;}
.foot .select-all{width:80px;height:48px;line-height:48px;color:#666;text-align:center;}
.foot .delete{padding-left:10px;}
.foot .closing{border-left:1px solid #c8c8c8;width:103px;text-align:center;color:#666;font-weight:bold;cursor:pointer;background-image:linear-gradient(RGB(241,241,241),RGB(226,226,226));}
.foot .closing:hover{background-image:linear-gradient(RGB(226,226,226),RGB(241,241,241));color:#333;}
.foot .total{margin:0 20px;cursor:pointer;}
.foot  #priceTotal, .foot #selectedTotal{color:red;font-family:"Microsoft Yahei";font-weight:bold;}
.foot .selected{cursor:pointer;}
.foot .selected .arrow{position:relative;top:-3px;margin-left:3px;}
.foot .selected .down{position:relative;top:3px;display:none;}
.show .selected .down{display:inline;}
.show .selected .up{display:none;}
.foot .selected:hover .arrow{color:red;}
.foot .selected-view{width:938px;border:1px solid #c8c8c8;position:absolute;height:auto;background:#ffffff;z-index:9;bottom:48px;left:-1px;display:none;}
.show .selected-view{display:block;}
.foot .selected-view div{height:auto;}
.foot .selected-view .arrow{font-size:16px;line-height:100%;color:#c8c8c8;position:absolute;right:330px;bottom:-9px;}
.foot .selected-view .arrow span{color:#ffffff;position:absolute;left:0px;bottom:1px;}

#selectedViewList
{padding:10px 20px 10px 20px;}

#selectedViewList div
{display:inline-block;
position:relative;
width:100px;
height:80px;
border:1px solid #ccc;
margin:10px;
float:left;}

#selectedViewList div img{width:100px;height:80px;margin-right:10px;float:left;}
#selectedViewList div span{display:none;color:#ffffff;font-size:12px;position:absolute;top:0px;right:0px;width:60px;height:18px;line-height:18px;text-align:center;background:#000;cursor:pointer;}
#selectedViewList div:hover span{display:block;}