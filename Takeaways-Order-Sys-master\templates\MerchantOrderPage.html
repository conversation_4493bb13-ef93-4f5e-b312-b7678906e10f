<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>个人中心</title>
	<link rel="stylesheet" href="static/css/OrderPage.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="static/js/modernizr-2.6.2.min.js"></script>

    <link href="https://fonts.googleapis.com/css?family=Lato:300,400,700" rel="stylesheet">
	<link href="https://fonts.googleapis.com/css?family=<PERSON><PERSON><PERSON>+<PERSON>ript" rel="stylesheet">
	<!-- Animate.css -->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/animate.css') }}">
	<!-- Icomoon Icon Fonts-->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/icomoon.css') }}">
	<!-- Themify Icons-->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/themify-icons.css') }}">
	<!-- Bootstrap  -->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.css') }}">
	<!-- Magnific Popup -->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/magnific-popup.css') }}">
	<!-- Bootstrap DateTimePicker -->
	<link rel="stylesheet" href="static/css/bootstrap-datetimepicker.min.css">
	<!-- Owl Carousel  -->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/owl.carousel.min.css') }}">
	<link rel="stylesheet" href="{{ url_for('static', filename='css/owl.theme.default.min.css') }}">


</head>
<body>
	<div class="navbkg" >
    <nav class="gtco-nav" role="navigation" style="background-color: rgba(0, 0, 0, 0.8);">
        <div class="gtco-container">
            <div class="row">
                <div class="col-sm-4 col-xs-12">
                    <div id="gtco-logo"><a href="MerchantOrderPage">查看订单 <em>.</em></a></div>
                </div>
                <div class="col-xs-8 text-right menu-1">
                    <ul>
                        <li class="has-dropdown">
                            <a href="MerchantIndex">其他功能</a>
                            <ul class="dropdown">
								<li><a href="MerchantModifyPerInfo">修改个人信息</a></li>
								<li><a href="MerchantModifyPwd">修改密码</a></li>
                                <li><a href="ResCommentList">查看评论</a></li>
                                <li><a href="MerchantMenu">菜单列表</a></li>

                            </ul>
						</li>
						<li><a href="MerchantIndex">返回首页</a></li>
                    </ul>
                </div>
            </div>
        </div>
	</nav>
	</div>

	<div class="gtco-section">
		<div class="gtco-container">
			<!-- 文字 -->
			<div class="row">
				<div class="col-md-8 col-md-offset-2 text-center gtco-heading">
					<p style="font-size: 40px; color: #FBB448; font-family: Helvetica;">收到的订单</p>
					<!-- <p>Dignissimos asperiores vitae velit veniam totam fuga molestias accusamus alias autem provident. Odit ab aliquam dolor eius.</p> -->
				</div>
			</div>
			<!-- TODO: 此处还想加一个按照时间顺序或价格排列 -->
			<div class="selectcondition">
				<form action="{{ url_for('MerchantOrderPage') }}" method="post">
					<input type="submit" name="action" value="按时间排序" style="color: white; font-size: 16px; outline:none; border:0px; background-color: #FBB448; border-radius: 10px;">
					<input type="submit" name="action" value="按价格排序" style="color: white; font-size: 16px; outline:none; border:0px; background-color: #FBB448; border-radius: 10px;">
					<span class="badge" style="float: right;">{{ notFinishedNum }}</span><input type="submit" name="action" value="未完成订单" style="background-color: transparent; color: #FBB448; float: right; font-size: 16px; outline:none; border:0px;">
				</form>
			</div>
			<br/><br/>
			<!-- 展示我的全部订单 -->
			<div class="row">
				<div class="col-lg-12 col-md-4 col-sm-6">
					{% if messages == "done" %}
						{% for item in result %}
							<a href="{{ url_for('static', filename=item[10]) if item[10] else '#' }}" class="fh5co-card-item image-popup">
								<figure class="col-lg-4" style="float: left;">
									<div class="overlay"><i class="ti-plus"></i></div>
									<img src="{{ url_for('static', filename=item[10]) if item[10] else url_for('static', filename='images/default.jpg') }}" alt="Image" class="img-responsive">
								</figure>
								<div class="fh5co-text">
									<h2 style="margin-top: 40px;"> 菜品：{{ item[2] }} </h2>
									<p>订单号：{{ item[0] }}</p>
									<p>交易时间：{{ item[9] }}</p>
									<!-- mode=0：外卖，mode=1：堂食 -->
									{% if item[4] == 1 %}
										<p>就餐方式：堂食</p>
										<p>餐厅地址：{{ item[6] }}</p>
										<p>取餐时间：{{ item[5] }}</p>
									{% elif item[4] == 0 %}
										<p>就餐方式：外卖</p>
										<p>配送地址：{{ item[6] }}</p>
										<p>送达时间：{{ item[5] }}</p>
									{% endif %}
									{% if item[6] == 1 %}
										<p>订单状态：已完成</p>
									{% elif item[6] == 0 %}
										<p>订单状态：<span style="color: red;">未完成</span></p>
										<form action="{{ url_for('ProcessOrder') }}" method="POST" style="display: inline;">
											<input name="orderID" value="{{ item[0] }}" style="display: none;">
											<input type="submit" name="action" value="完成订单" style="margin: 5px; font-size: 14px; color: white; background-color: #28a745; border: none; border-radius: 5px; padding: 5px 10px;" onclick="return confirm('确定要完成这个订单吗？');">
											<input type="submit" name="action" value="取消订单" style="margin: 5px; font-size: 14px; color: white; background-color: #dc3545; border: none; border-radius: 5px; padding: 5px 10px;" onclick="return confirm('确定要取消这个订单吗？');">
										</form>
									{% endif %}
									<p>订单总价格：<span class="price cursive-font">￥{{ item[4] * item[5] }}</span></p>
								</div>
							</a>
						{% endfor %}

					{% elif messages == "none" %}
						<!-- <p style="text-align: center;"><strong style="font-size: 18px;">您还没有订单哦！</strong></p> -->
						<div class="alert alert-danger" role="alert">您还没有订单哦！</div>
					{% endif %}

				</div>

			</div>
		</div>
	</div>
	<div>
	</div>


    	<!-- jQuery -->
	<script src="static/js/jquery.min.js"></script>
	<!-- jQuery Easing -->
	<script src="static/js/jquery.easing.1.3.js"></script>
	<!-- Bootstrap -->
	<script src="static/js/bootstrap.min.js"></script>
	<!-- Waypoints -->
	<script src="static/js/jquery.waypoints.min.js"></script>
	<!-- Carousel -->
	<script src="static/js/owl.carousel.min.js"></script>
	<!-- countTo -->
	<script src="static/js/jquery.countTo.js"></script>

	<!-- Stellar Parallax -->
	<script src="static/js/jquery.stellar.min.js"></script>

	<!-- Magnific Popup -->
	<script src="static/js/jquery.magnific-popup.min.js"></script>
	<script src="static/js/magnific-popup-options.js"></script>

	<script src="static/js/moment.min.js"></script>
	<script src="static/js/bootstrap-datetimepicker.min.js"></script>


	<!-- Main -->
	<script src="static/js/main.js"></script>
</body>
</html>