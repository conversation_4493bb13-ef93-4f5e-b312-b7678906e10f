<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>菜单界面</title>
	<link rel="stylesheet" href="static/css/OrderPage.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="static/js/modernizr-2.6.2.min.js"></script>

    <link href="https://fonts.googleapis.com/css?family=Lato:300,400,700" rel="stylesheet">
	<link href="https://fonts.googleapis.com/css?family=<PERSON><PERSON><PERSON>+<PERSON>ript" rel="stylesheet">
	<!-- Animate.css -->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/animate.css') }}">
	<!-- Icomoon Icon Fonts-->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/icomoon.css') }}">
	<!-- Themify Icons-->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/themify-icons.css') }}">
	<!-- Bootstrap  -->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.css') }}">
	<!-- Magnific Popup -->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/magnific-popup.css') }}">
	<!-- Bootstrap DateTimePicker -->
	<link rel="stylesheet" href="static/css/bootstrap-datetimepicker.min.css">
	<!-- Owl Carousel  -->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/owl.carousel.min.css') }}">
	<link rel="stylesheet" href="{{ url_for('static', filename='css/owl.theme.default.min.css') }}">


</head>
<body>
	<div class="navbkg" >
    <nav class="gtco-nav" role="navigation" style="background-color: rgba(0, 0, 0, 0.8);">
        <div class="gtco-container">
            <div class="row">
                <div class="col-sm-4 col-xs-12">
                    <div id="gtco-logo"><a href="MerchantMenu">维护菜单<em></em></a></div>
                </div>
                <div class="col-xs-8 text-right menu-1">
                    <ul>
                        <li class="has-dropdown">
                            <a href="MerchantIndex">其他功能</a>
                            <ul class="dropdown">
								<li><a href="MerchantModifyPerInfo">修改个人信息</a></li>
								<li><a href="MerchantModifyPwd">修改密码</a></li>
                                <li><a href="ResCommentList">查看评论</a></li>
                                <li><a href="MerchantOrderPage">查看订单</a></li>

                            </ul>
						</li>
                        <li><a href="MerchantIndex">返回首页</a></li>
                    </ul>
                </div>
            </div>
        </div>
	</nav>
	</div>

	<div class="gtco-section">
		<div class="gtco-container">
			<!-- 文字 -->
			<div class="row">
				<div class="col-md-8 col-md-offset-2 text-center gtco-heading">
					<p style="font-size: 40px; color: #FBB448; font-family: Helvetica;">我的菜单</p>
					<!-- <p>Dignissimos asperiores vitae velit veniam totam fuga molestias accusamus alias autem provident. Odit ab aliquam dolor eius.</p> -->
				</div>
			</div>
			<!-- TODO: 此处还想加一个按照时间顺序或价格排列 -->
			<div class="selectcondition">
				<form action="{{ url_for('MerchantMenu') }}" method="post">
					<input type="submit" name="action" value="按销量排序" style="color: white; font-size: 16px; outline:none; border:0px; background-color: #FBB448; border-radius: 10px;">
					<input type="submit" name="action" value="按价格排序" style="color: white; font-size: 16px; outline:none; border:0px; background-color: #FBB448; border-radius: 10px;">
				</form>
                <form action="{{ url_for('MenuAdd') }}" method="POST">
                    <input name="restaurant" value="{{ username }}" style="display: none;">
                    <input type="submit" name="action" value="增加菜品" style="margin: 15px; font-size: 16px; float: right; color: #FBB448; background-color: white; font-size: 16px; outline:none; border: 1px solid #FBB448; border-radius: 10px;">
                </form>
			</div>
			<br/><br/>
			<!-- 展示我的全部订单 -->
			<div class="row">
				<div class="col-lg-12 col-md-4 col-sm-6">
					{% if messages == "done" %}
						{% for item in result %}
							<div class="fh5co-card-item">
								<figure class="col-lg-4" style="float: left;">
									<div class="overlay"><i class="ti-plus"></i></div>
									<img src="{{ url_for('static', filename=item[6]) if item[6] else url_for('static', filename='images/default.jpg') }}" alt="Image" class="img-responsive">
								</figure>
								<div class="fh5co-text">
                                    {% if item[7] == 1 %}
                                    <h1 style="margin-top: 40px;">特色菜</h1>
                                    {% endif %}
									<h2 style="margin-top: 40px;"> 菜品：{{ item[0] }} </h2>
									<p>菜品描述：{{ item[2] }}</p>
									<p>营养成分：{{ item[3] }}</p>
                                    <p>销量：{{item[5] }}</p>
									<p style="margin-left: 322px;">价格：<span class="price cursive-font">￥{{ item[4]}}</span></p>
                                    <form action="{{ url_for('MenuModify') }}" method="POST">
                                        <input name="dishname" value="{{ item[0] }}" style="display: none;">
                                        <input name="restaurant" value="{{ item[1] }}" style="display: none;">
                                        <input name="dishinfo" value="{{ item[2] }}" style="display: none;">
                                        <input name="nutriention" value="{{ item[3] }}" style="display: none;">
                                        <input name="price" value="{{ item[4] }}" style="display: none;">
                                        <input name="isSpecialty" value="{{ item[7] }}" style="display: none;">
                                        <input type="submit" name="action" value="修改菜品信息" style="margin: 15px; font-size: 16px; float: right; color: #FBB448; background-color: white; font-size: 16px; outline:none; border: 1px solid #FBB448; border-radius: 10px;">
                                    </form>
                                    <form action="{{ url_for('DeleteDish') }}" method="POST">
                                        <input name="dishname" value="{{ item[0] }}" style="display: none;">
                                        <input name="restaurant" value="{{ item[1] }}" style="display: none;">
                                        <input type="submit" name="action" value="删除该菜品" style="margin: 15px; font-size: 16px; float: right; color: #FBB448; background-color: white; font-size: 16px; outline:none; border: 1px solid #FBB448; border-radius: 10px;" onclick="return confirm('确定要删除这个菜品吗？');">
                                    </form>

                                    <br/>
								</div>
							</div>
						{% endfor %}
					<!-- TODO -->


					{% elif messages == "none" %}
						<!-- <p style="text-align: center;"><strong style="font-size: 18px;">您还没有订单哦！</strong></p> -->
						<div class="alert alert-danger" role="alert">您还没有菜单哦！</div>
					{% endif %}

                    {% if dmessages == "done" %}
                        <script>alert("删除菜品"+"{{dishname}}"+"成功")</script>
                        <script language="javascript" type="text/javascript">
                            // 以下方式定时跳转
                            setTimeout("javascript:location.href='MerchantMenu'", 1);
                        </script>
                    {% elif dmessages == "fail" %}
                        <script>alert("数据库错误！")</script>
                    {% endif %}

				</div>

			</div>
		</div>
	</div>
	<div>
	</div>


    	<!-- jQuery -->
	<script src="static/js/jquery.min.js"></script>
	<!-- jQuery Easing -->
	<script src="static/js/jquery.easing.1.3.js"></script>
	<!-- Bootstrap -->
	<script src="static/js/bootstrap.min.js"></script>
	<!-- Waypoints -->
	<script src="static/js/jquery.waypoints.min.js"></script>
	<!-- Carousel -->
	<script src="static/js/owl.carousel.min.js"></script>
	<!-- countTo -->
	<script src="static/js/jquery.countTo.js"></script>

	<!-- Stellar Parallax -->
	<script src="static/js/jquery.stellar.min.js"></script>

	<!-- Magnific Popup -->
	<script src="static/js/jquery.magnific-popup.min.js"></script>
	<script src="static/js/magnific-popup-options.js"></script>

	<script src="static/js/moment.min.js"></script>
	<script src="static/js/bootstrap-datetimepicker.min.js"></script>


	<!-- Main -->
	<script src="static/js/main.js"></script>
</body>
</html>