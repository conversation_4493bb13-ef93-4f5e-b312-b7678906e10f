<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
    <title>注册</title>
    <!-- <link rel="stylesheet" href="static/css/test.css" type="text/css"> -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/test.css') }}">
    <link href="https://fonts.googleapis.com/css?family=Lato:300,400,700" rel="stylesheet">
	<link href="https://fonts.googleapis.com/css?family=<PERSON><PERSON><PERSON>+<PERSON>ript" rel="stylesheet">

	<!-- Animate.css -->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/animate.css') }}">
	<!-- Icomoon Icon Fonts-->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/icomoon.css') }}">
	<!-- Themify Icons-->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/themify-icons.css') }}">
	<!-- Bootstrap  -->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.css') }}">

	<!-- Magnific Popup -->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/magnific-popup.css') }}">

	<!-- Bootstrap DateTimePicker -->
	<link rel="stylesheet" href="static/css/bootstrap-datetimepicker.min.css">

	<!-- Owl Carousel  -->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/owl.carousel.min.css') }}">
	<link rel="stylesheet" href="{{ url_for('static', filename='css/owl.theme.default.min.css') }}">

	<!-- Theme style  -->
	<link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
	<script src="static/js/modernizr-2.6.2.min.js"></script>
<script>
	window.onload = function() {
		var phone = document.getElementById("phone");
		phone.onkeyup = function() {
			checkPhone();
		}
	}

	function checkPhone() {
		console.log("验证电话")
		var phone = document.getElementById('phone').value;
		if(!(/^1[3456789]\d{9}$/.test(phone))) {
			document.getElementById('checkPhone').innerHTML = "电话号码不规范";
			alert("手机号码格式有误，请重填");  
			return false; 
		} 
	}

</script>


</head>
<body>
	<div class="gtco-loader"></div>

	<div id="page">


	<!-- <div class="page-inner"> -->
	<nav class="gtco-nav" role="navigation">
		<div class="gtco-container">

			<div class="row">
				<div class="col-sm-4 col-xs-12">
					<div id="gtco-logo">注册 <em></em></div>
				</div>
				<div class="col-xs-8 text-right menu-1">
					<ul>
						<li><a href="index">返回首页</a></li>
						<li class="btn-cta"><a href="logIn"><span>登录</span></a></li>
					</ul>
				</div>
			</div>

		</div>
	</nav>

	<header id="gtco-header" class="gtco-cover gtco-cover-md" role="banner" style="background-image: url('static/images/img_bg_1.jpg')" data-stellar-background-ratio="0.5">
		<div class="overlay"></div>
		<div class="gtco-container">
			<div class="row">
				<div class="col-md-12 col-md-offset-0 text-left">

					<div class="row row-mt-15em">
						<div class="col-md-8 col-md-push-1 animate-box" data-animate-effect="fadeInRight">
							<div class="form-wrap">
								<div class="tab">

									<div class="tab-content">
										<div class="tab-content-inner active" data-content="signup">
											<h3 class="cursive-font">请注册</h3>
											<form action="" method="post">
												<div class="row form-group">
													<div class="col-md-12">
														<label for="activities">身份</label><br>
    													<input type="radio" name="userRole" value="RESTAURANT" />商家用户
														<input type="radio" name="userRole" value="CUSTOMER" checked />买家用户<br>
													</div>
												</div>
												<div class="row form-group">
													<div class="col-md-12">
														<label for="activities">用户名</label>
														<input type="text" name="username" placeholder="不可为空" class="form-control">
													</div>
												</div>
												<div class="row form-group">
													<div class="col-md-12">
														<label for="activities">密码</label>
														<input type="password" name="password" placeholder="不可为空" class="form-control">
													</div>
												</div>
												<div class="row form-group">
													<div class="col-md-12">
														<label for="activities">电话</label><span id="checkPhone"></span>
														<input type="text" name="phone" placeholder="不可为空" class="form-control form-input" id="contact-phone-2" data-constraints="@Numeric">
													</div>
												</div>
												<div class="row form-group">
													<div class="col-md-12">
														<label for="activities">地址</label>
														<input type="text" name="addr" placeholder="不可为空" class="form-control">
													</div>
												</div>

												<div class="row form-group">
													<div class="col-md-12">
														<input type="submit" class="btn btn-primary btn-block" name="submit" value="注册">
													</div>
												</div>
											</form>
											{% if messages == "done1" %}
												<script>alert("商家注册成功！")</script>
											{% elif messages == "fail1" %}
                                                <script>alert("注册失败！商家已注册。")</script>
											{% elif messages == "done2" %}
												<script>alert("买家用户注册成功！")</script>
											{% elif messages == "fail2" %}
                                                <script>alert("注册失败！买家已注册。")</script>
											{% endif %}
											<div class="col-md-12">
											</div>
										</div>


									</div>
								</div>
							</div>
						</div>
					</div>


				</div>
			</div>
		</div>
	</header>


<!--	<footer id="gtco-footer" role="contentinfo" style="background-image: url(static/images/img_bg_1.jpg)" data-stellar-background-ratio="0.5">-->
<!--		<div class="overlay"></div>-->
<!--	</footer>-->

	</div>

	<div class="gototop js-top">
		<a href="#" class="js-gotop"><i class="icon-arrow-up"></i></a>
	</div>

	<!-- jQuery -->
	<script src="static/js/jquery.min.js"></script>
	<!-- jQuery Easing -->
	<script src="static/js/jquery.easing.1.3.js"></script>
	<!-- Bootstrap -->
	<script src="static/js/bootstrap.min.js"></script>
	<!-- Waypoints -->
	<script src="static/js/jquery.waypoints.min.js"></script>
	<!-- Carousel -->
	<script src="static/js/owl.carousel.min.js"></script>
	<!-- countTo -->
	<script src="static/js/jquery.countTo.js"></script>

	<!-- Stellar Parallax -->
	<script src="static/js/jquery.stellar.min.js"></script>

	<!-- Magnific Popup -->
	<script src="static/js/jquery.magnific-popup.min.js"></script>
	<script src="static/js/magnific-popup-options.js"></script>

	<script src="static/js/moment.min.js"></script>
	<script src="static/js/bootstrap-datetimepicker.min.js"></script>


	<!-- Main -->
	<script src="static/js/main.js"></script>
</body>

</html>