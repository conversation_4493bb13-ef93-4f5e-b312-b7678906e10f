# 阿楠の外卖屋 - 使用说明

## 🚀 快速启动

1. **启动系统**：
   ```bash
   .\new_venv\Scripts\python.exe app_full.py
   ```

2. **访问地址**：http://localhost:5000

3. **测试账号**：
   - 管理员: root / 12345678
   - 顾客: 阿楠 / 77777777  
   - 商家: 土风土味 / 77777777

## 📱 功能介绍

### 🏠 首页功能
- 系统介绍和导航
- 登录/注册入口

### 👤 用户注册登录
- 支持三种用户类型：顾客、商家、管理员
- 新用户可以注册顾客或商家账号
- 登录后根据用户类型跳转到对应页面

### 🛒 顾客功能

#### 1. 餐厅浏览 (`/UserRestList`)
- 查看所有可用餐厅
- 点击"进入本店"浏览菜单
- 点击"查看评价"查看餐厅评论

#### 2. 菜单浏览 (`/Menu`)
- 查看餐厅所有菜品
- 查看菜品详情（价格、描述、营养信息）
- 点击"加入购物车"添加菜品

#### 3. 购物车管理 (`/myOrder`)
- 查看已添加的菜品
- 删除不需要的菜品
- 点击"结算"提交订单

#### 4. 订单管理 (`/OrderPage`)
- 查看所有历史订单
- 查看订单状态（未完成/已完成）
- 对已完成订单进行评价

#### 5. 评价系统 (`/CommentForm`)
- 对已完成订单打分（1-5星）
- 填写文字评价

#### 6. 个人中心 (`/personal`)
- 查看个人信息
- 修改个人资料

### 🏪 商家功能

#### 1. 商家主页 (`/MerchantIndex`)
- 商家操作导航
- 快速访问各功能模块

#### 2. 菜单管理 (`/MerchantMenu`)
- 查看自己的所有菜品
- 添加新菜品 (`/AddDish`)
- 删除菜品 (`/DeleteDish`)
- 修改菜品信息

#### 3. 订单管理 (`/MerchantOrderPage`)
- 查看所有订单
- 处理订单状态：
  - 标记订单为"已完成"
  - 取消订单
- 查看订单详情和顾客信息

#### 4. 个人中心 (`/MerchantPersonal`)
- 查看商家信息
- 修改商家资料

### 👨‍💼 管理员功能

#### 1. 管理员主页 (`/admin`)
- 系统管理导航
- 查看系统统计信息

## 🎯 使用流程示例

### 顾客下单流程：
1. 注册/登录顾客账号
2. 浏览餐厅列表 → 选择餐厅
3. 浏览菜单 → 添加菜品到购物车
4. 查看购物车 → 确认订单并结算
5. 在订单页面查看订单状态
6. 订单完成后进行评价

### 商家处理订单流程：
1. 登录商家账号
2. 查看订单管理页面
3. 处理新订单（标记为完成或取消）
4. 管理菜单（添加/删除菜品）
5. 查看顾客评价

## 🔧 技术特点

- **数据库**：使用SQLite，无需复杂配置
- **框架**：Flask 2.1.1 + Werkzeug 2.3.8
- **模板**：使用原项目的26个HTML模板
- **功能完整**：包含注册、登录、购物车、订单、评价等完整功能
- **用户友好**：适合小白用户快速上手

## 📊 数据库表结构

- **ADMIN**: 管理员表
- **CUSTOMER**: 顾客表  
- **RESTAURANT**: 商家表
- **DISHES**: 菜品表
- **SHOPPINGCART**: 购物车表
- **ORDER_COMMENT**: 订单评论表

## 🛠️ 故障排除

1. **端口占用**：如果5000端口被占用，修改app_full.py最后一行的端口号
2. **模板错误**：确保在正确目录运行（包含templates文件夹的目录）
3. **数据库错误**：删除takeaway.db文件，重新启动系统自动重建

## 📝 注意事项

- 这是开发版本，仅用于学习和测试
- 生产环境请使用专业的WSGI服务器
- 建议定期备份数据库文件
